"""
文件分类功能演示
"""
import os
import tempfile
from pathlib import Path
from enhanced_rule_manager import EnhancedRuleManager
from folder_analyzer import FolderAnalyzer


def create_demo_structure():
    """创建演示用的混合文件结构"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="oxford_demo_")
    base_path = Path(temp_dir) / "牛津教材整理演示"
    base_path.mkdir(parents=True)
    
    print(f"📁 创建演示文件夹: {base_path}")
    
    # 创建混合的文件和文件夹结构
    demo_items = [
        # 文件夹
        ("文件夹", "4A数学资料"),
        ("文件夹", "5B英语材料"),
        ("文件夹", "中四综合练习"),
        ("文件夹", "中五期末复习"),
        ("文件夹", "教师用书合集"),
        ("文件夹", "练习册整理"),
        
        # 文件
        ("文件", "4A数学课本.pdf"),
        ("文件", "5B英语听力.mp3"),
        ("文件", "中四数学试卷.docx"),
        ("文件", "中五英语作文.pdf"),
        ("文件", "教师用书-4A数学.pdf"),
        ("文件", "练习册-5B英语.pdf"),
        ("文件", "4A练习答案.pdf"),
        ("文件", "5B课件PPT.pptx"),
        ("文件", "数学公式表.pdf"),
        ("文件", "英语词汇表.docx"),
    ]
    
    for item_type, item_name in demo_items:
        if item_type == "文件夹":
            folder = base_path / item_name
            folder.mkdir(parents=True, exist_ok=True)
            # 在文件夹中创建一些示例文件
            (folder / "示例文件.txt").write_text(f"这是{item_name}中的示例文件", encoding='utf-8')
            print(f"  📁 {item_name}/")
        else:
            file = base_path / item_name
            file.write_text(f"这是{item_name}的内容", encoding='utf-8')
            print(f"  📄 {item_name}")
    
    return str(base_path)


def demo_file_classification():
    """演示文件分类功能"""
    print("🎯 牛津教材文件分类系统演示")
    print("=" * 50)
    
    # 创建演示结构
    source_path = create_demo_structure()
    
    # 创建规则管理器并启用常用规则
    print("\n⚙️ 配置分类规则...")
    rm = EnhancedRuleManager()
    
    # 启用常用规则
    common_rules = ["中四", "中五", "数学", "英语", "练习册", "教师用书"]
    for rule in common_rules:
        rm.enable_rule(rule, True)
        print(f"  ✅ 启用规则: {rule}")
    
    # 创建分析器
    fa = FolderAnalyzer(rm)
    fa.set_source_path(source_path)
    
    # 扫描和分析
    print(f"\n🔍 扫描文件结构...")
    folder_structure, file_structure = fa.scan_all_items()
    
    print(f"  📁 文件夹: {len(folder_structure)}个")
    print(f"  📄 文件: {len(file_structure)}个")
    
    # 分析匹配
    print(f"\n🎯 智能分类分析...")
    matches = fa.analyze_matches()
    
    folder_matches = [m for m in matches if m.item_info.is_dir]
    file_matches = [m for m in matches if not m.item_info.is_dir]
    
    print(f"  📁 文件夹匹配: {len(folder_matches)}个")
    print(f"  📄 文件匹配: {len(file_matches)}个")
    
    # 显示分类结果
    print(f"\n📋 分类结果预览:")
    
    # 按分类组织结果
    categories = {}
    for match in matches:
        if match.category not in categories:
            categories[match.category] = []
        categories[match.category].append(match)
    
    for category, items in categories.items():
        print(f"\n  📂 {category} ({len(items)}个项目):")
        for item in items:
            item_type = "📁" if item.item_info.is_dir else "📄"
            print(f"    {item_type} {item.item_info.name} (匹配: {item.matched_keyword})")
    
    # 生成移动操作
    print(f"\n🚀 生成整理方案...")
    target_base_path = str(Path(source_path).parent / "整理后的牛津教材")
    operations = fa.generate_move_operations(target_base_path)
    
    print(f"  总操作数: {len(operations)}个")
    print(f"  目标路径: {target_base_path}")
    
    # 显示整理方案
    print(f"\n📝 整理方案详情:")
    for i, op in enumerate(operations, 1):
        source_rel = str(Path(op.source_path).relative_to(source_path))
        target_rel = str(Path(op.target_path).relative_to(target_base_path))
        item_type = "📁" if op.item_info.is_dir else "📄"
        print(f"  {i:2d}. {item_type} {source_rel}")
        print(f"      → {target_rel}")
    
    # 统计信息
    print(f"\n📊 统计信息:")
    total_size = sum(op.item_info.size for op in operations)
    categories_count = len(set(op.category for op in operations))
    
    print(f"  📁 文件夹操作: {len([op for op in operations if op.item_info.is_dir])}个")
    print(f"  📄 文件操作: {len([op for op in operations if not op.item_info.is_dir])}个")
    print(f"  📂 涉及分类: {categories_count}个")
    print(f"  💾 总大小: {total_size} 字节")
    
    print(f"\n✨ 演示完成！")
    print(f"📁 演示文件夹: {source_path}")
    print(f"💡 提示: 你可以在GUI程序中选择这个文件夹来查看实际效果")
    
    return source_path


if __name__ == "__main__":
    demo_file_classification()
