"""
测试所有修复的功能
"""
import os
import tempfile
from pathlib import Path
from enhanced_rule_manager import EnhancedRuleManager
from folder_analyzer import FolderAnalyzer


def test_default_rules():
    """测试默认规则启用状态"""
    print("=== 测试默认规则启用状态 ===")
    
    # 删除现有规则文件以确保使用默认设置
    rule_file = Path("enhanced_oxford_rules.json")
    if rule_file.exists():
        rule_file.unlink()
    
    rm = EnhancedRuleManager()
    
    # 检查统计信息
    stats = rm.get_statistics()
    print(f"规则统计: 总计{stats['total_rules']}个规则，启用{stats['enabled_rules']}个，禁用{stats['disabled_rules']}个")
    
    # 显示启用的规则
    enabled_rules = rm.get_enabled_rules()
    print(f"默认启用的规则: {list(enabled_rules.keys())}")
    
    # 验证期望的规则是否启用
    expected_enabled = ["中一", "中二", "中三", "中四", "中五", "中六", "数学", "英语", "练习册", "教师用书"]
    actually_enabled = list(enabled_rules.keys())
    
    print(f"期望启用: {expected_enabled}")
    print(f"实际启用: {actually_enabled}")
    
    missing = set(expected_enabled) - set(actually_enabled)
    extra = set(actually_enabled) - set(expected_enabled)
    
    if missing:
        print(f"❌ 缺少启用的规则: {missing}")
    if extra:
        print(f"ℹ️  额外启用的规则: {extra}")
    
    if not missing:
        print("✅ 默认规则启用状态正确")
    
    return rm


def test_rule_editing():
    """测试规则编辑功能"""
    print("\n=== 测试规则编辑功能 ===")
    
    rm = EnhancedRuleManager()
    
    # 测试添加规则
    print("--- 测试添加规则 ---")
    original_count = len(rm.get_all_rules())
    rm.add_rule("测试分类", ["测试1", "测试2"], enabled=True)
    new_count = len(rm.get_all_rules())
    
    if new_count == original_count + 1:
        print("✅ 添加规则成功")
    else:
        print("❌ 添加规则失败")
    
    # 测试修改规则
    print("--- 测试修改规则 ---")
    rm.rules["测试分类"]["keywords"] = ["修改后1", "修改后2", "修改后3"]
    rm.rules["测试分类"]["enabled"] = False
    rm.save_rules()
    
    # 重新加载验证
    rm2 = EnhancedRuleManager()
    test_rule = rm2.get_all_rules().get("测试分类")
    if test_rule and test_rule["keywords"] == ["修改后1", "修改后2", "修改后3"] and not test_rule["enabled"]:
        print("✅ 修改规则成功")
    else:
        print("❌ 修改规则失败")
    
    # 测试删除规则
    print("--- 测试删除规则 ---")
    rm.remove_rule("测试分类")
    final_count = len(rm.get_all_rules())
    
    if final_count == original_count:
        print("✅ 删除规则成功")
    else:
        print("❌ 删除规则失败")


def test_path_structure():
    """测试路径结构生成"""
    print("\n=== 测试路径结构生成 ===")
    
    # 创建测试结构
    temp_dir = tempfile.mkdtemp(prefix="path_structure_test_")
    base_path = Path(temp_dir) / "测试文件夹"
    base_path.mkdir(parents=True)
    
    # 创建复杂的文件夹结构
    test_folders = [
        "A/4A内容",
        "A/5A内容", 
        "B/4B内容",
        "B/5B内容",
        "C/包含4A的文件夹",
        "练习册/4A练习",
        "练习册/5B练习",
        "教师用书/4A教师版"
    ]
    
    for folder_path in test_folders:
        folder = base_path / folder_path
        folder.mkdir(parents=True, exist_ok=True)
        test_file = folder / "测试文件.txt"
        test_file.write_text("测试内容", encoding='utf-8')
    
    # 设置规则管理器
    rm = EnhancedRuleManager()
    rm.enable_rule("中四", True)
    rm.enable_rule("中五", True)
    rm.enable_rule("练习册", True)
    rm.enable_rule("教师用书", True)
    
    # 分析文件夹
    fa = FolderAnalyzer(rm)
    fa.set_source_path(str(base_path))
    fa.scan_folder_structure()
    
    matches = fa.analyze_matches()
    target_base_path = str(Path(base_path).parent / "整理后")
    
    print("路径映射结果:")
    expected_mappings = {
        "A\\4A内容": "中四\\A\\4A内容",
        "A\\5A内容": "中五\\A\\5A内容", 
        "B\\4B内容": "中四\\B\\4B内容",
        "B\\5B内容": "中五\\B\\5B内容",
        "C\\包含4A的文件夹": "中四\\C\\包含4A的文件夹",
        "练习册\\4A练习": "中四\\练习册\\4A练习",
        "练习册\\5B练习": "中五\\练习册\\5B练习",
        "教师用书\\4A教师版": "中四\\教师用书\\4A教师版"
    }
    
    actual_mappings = {}
    for match in matches:
        source_rel = str(Path(match.item_info.path).relative_to(base_path))
        target_path = fa._generate_target_path(match, target_base_path)
        target_rel = str(Path(target_path).relative_to(target_base_path))
        actual_mappings[source_rel] = target_rel
        print(f"  {source_rel} → {target_rel}")
    
    # 验证路径映射
    print("\n验证路径映射:")
    all_correct = True
    for source, expected_target in expected_mappings.items():
        if source in actual_mappings:
            actual_target = actual_mappings[source]
            if actual_target == expected_target:
                print(f"✅ {source} → {actual_target}")
            else:
                print(f"❌ {source} → {actual_target} (期望: {expected_target})")
                all_correct = False
        else:
            print(f"❌ 缺少映射: {source}")
            all_correct = False
    
    if all_correct:
        print("✅ 所有路径映射正确")
    else:
        print("❌ 存在路径映射错误")
    
    print(f"测试文件夹位置: {base_path}")


if __name__ == "__main__":
    print("开始测试所有修复的功能...")
    
    try:
        test_default_rules()
        test_rule_editing()
        test_path_structure()
        
        print("\n=== 测试完成 ===")
        print("所有功能测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
