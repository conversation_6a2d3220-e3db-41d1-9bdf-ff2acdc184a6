"""
牛津教材分类整理器测试脚本
用于测试各个组件的基本功能
"""
import os
import tempfile
from pathlib import Path

from rule_manager import RuleManager
from folder_analyzer import FolderAnalyzer
from operation_executor import OperationExecutor, OperationType


def create_test_structure():
    """创建测试文件夹结构"""
    # 创建临时目录
    temp_dir = Path(tempfile.mkdtemp(prefix="oxford_test_"))
    
    # 创建源文件夹结构
    source_dir = temp_dir / "牛津新世代数学第三版"
    source_dir.mkdir()
    
    # 创建子文件夹
    folders = [
        "A/4A相关内容",
        "A/5A相关内容", 
        "A/其他内容",
        "B/4B相关内容",
        "B/5B相关内容",
        "C/包含4A的文件夹",
        "C/包含5B的文件夹",
        "D/无关内容"
    ]
    
    for folder in folders:
        folder_path = source_dir / folder
        folder_path.mkdir(parents=True, exist_ok=True)
        
        # 创建一些测试文件
        test_file = folder_path / "test.txt"
        test_file.write_text(f"测试文件 - {folder}")
    
    print(f"创建测试结构: {source_dir}")
    return str(source_dir)


def test_rule_manager():
    """测试规则管理器"""
    print("\n=== 测试规则管理器 ===")

    rm = RuleManager()

    # 清空现有规则，添加测试规则
    rm.rules = {}  # 清空现有规则
    rm.add_rule("四年级", ["4A", "4B"])
    rm.add_rule("五年级", ["5A", "5B"])
    
    # 获取规则
    rules = rm.get_all_rules()
    print(f"当前规则: {rules}")
    
    # 测试匹配
    matches = rm.match_folder_to_categories("4A相关内容")
    print(f"'4A相关内容' 匹配结果: {matches}")
    
    matches = rm.match_folder_to_categories("包含4A的文件夹")
    print(f"'包含4A的文件夹' 匹配结果: {matches}")
    
    # 验证规则
    errors = rm.validate_rules()
    print(f"规则验证结果: {errors if errors else '无错误'}")
    
    return rm


def test_folder_analyzer(rule_manager, source_path):
    """测试文件夹分析器"""
    print("\n=== 测试文件夹分析器 ===")
    
    fa = FolderAnalyzer(rule_manager)
    fa.set_source_path(source_path)
    
    # 扫描文件夹结构
    structure = fa.scan_folder_structure()
    print(f"扫描到 {len(structure)} 个文件夹")
    
    # 分析匹配
    matches = fa.analyze_matches()
    print(f"匹配到 {len(matches)} 个项目:")
    for match in matches:
        item_type = "文件夹" if match.item_info.is_dir else "文件"
        print(f"  - {match.item_info.name} ({item_type}) → {match.category} (关键词: {match.matched_keyword})")
    
    # 获取未匹配的文件夹
    unmatched = fa.get_unmatched_folders()
    print(f"未匹配的文件夹 ({len(unmatched)}个):")
    for folder in unmatched:
        print(f"  - {folder.name}")
    
    # 生成移动操作
    operations = fa.generate_move_operations()
    print(f"生成 {len(operations)} 个移动操作")

    # 显示详细的移动操作路径
    print("详细移动操作:")
    for i, op in enumerate(operations, 1):
        source_rel = Path(op.source_path).relative_to(source_path)
        target_rel = Path(op.target_path).relative_to(fa._generate_target_base_path())
        print(f"  {i:2d}. {source_rel} → {target_rel}")
    
    # 获取分析摘要
    summary = fa.get_analysis_summary()
    print(f"分析摘要: {summary}")
    
    return operations


def test_operation_executor(operations):
    """测试操作执行器（仅验证，不实际执行）"""
    print("\n=== 测试操作执行器 ===")
    
    def progress_callback(current, total, message):
        print(f"进度: {current}/{total} - {message}")
    
    oe = OperationExecutor(progress_callback)
    oe.set_operation_type(OperationType.COPY)  # 使用复制模式测试
    
    # 验证操作
    errors = oe.validate_operations(operations)
    print(f"操作验证结果: {errors if errors else '无错误'}")
    
    if not errors:
        print("操作验证通过，可以执行")
        # 注意：这里不实际执行操作，只是验证
        # result = oe.execute_operations(operations)
        # print(f"执行结果: 成功 {result.successful_operations}, 失败 {result.failed_operations}")
    
    return oe


def main():
    """主测试函数"""
    print("开始测试牛津教材分类整理器...")
    
    try:
        # 创建测试结构
        source_path = create_test_structure()
        
        # 测试规则管理器
        rule_manager = test_rule_manager()
        
        # 测试文件夹分析器
        operations = test_folder_analyzer(rule_manager, source_path)
        
        # 测试操作执行器
        test_operation_executor(operations)
        
        print("\n=== 测试完成 ===")
        print(f"测试文件夹位置: {source_path}")
        print("可以手动检查生成的文件夹结构")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
