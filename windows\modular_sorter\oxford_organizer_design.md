# 牛津教材分类整理器 - 设计文档

## 1. 需求概述

### 1.1 核心需求
设计一个智能文件分类整理工具，能够根据用户定义的规则，自动识别和整理教材文件夹结构。

### 1.2 典型使用场景
- **源文件夹**: `D:\BaiduNetdiskDownload\牛津新世代数学第三版`
- **源结构**: 
  ```
  牛津新世代数学第三版/
  ├── A/
  │   ├── 4A相关内容/
  │   ├── 5A相关内容/
  │   └── 其他内容/
  ├── B/
  │   ├── 4B相关内容/
  │   ├── 5B相关内容/
  │   └── 其他内容/
  └── C/
      ├── 包含4A的文件夹/
      ├── 包含5B的文件夹/
      └── 其他内容/
  ```

- **目标结构**:
  ```
  牛津新世代数学第三版-整理版本/
  ├── 四年级/
  │   ├── A/
  │   │   └── [4A相关内容]
  │   ├── B/
  │   │   └── [4B相关内容]
  │   └── C/
  │       └── [包含4A的内容]
  └── 五年级/
      ├── A/
      │   └── [5A相关内容]
      ├── B/
      │   └── [5B相关内容]
      └── C/
          └── [包含5B的内容]
  ```

## 2. 功能设计

### 2.1 主要功能模块

#### 2.1.1 文件夹输入模块
- **拖拽输入**: 支持拖拽源文件夹到界面
- **浏览选择**: 提供文件夹浏览按钮
- **路径显示**: 显示当前选择的源文件夹路径

#### 2.1.2 规则配置模块
- **分类规则定义**:
  - 关键词匹配规则 (如: "4A", "4B" → "四年级")
  - 支持正则表达式匹配
  - 支持模糊匹配 (包含关键词即可)
- **目标分类设置**:
  - 分类名称 (如: "四年级", "五年级")
  - 关键词列表 (如: ["4A", "4B"], ["5A", "5B"])
- **规则管理**:
  - 添加/删除分类规则
  - 规则优先级设置
  - 规则导入/导出功能

#### 2.1.3 预览分析模块
- **文件夹扫描**: 递归扫描源文件夹结构
- **匹配分析**: 根据规则分析哪些文件夹会被移动
- **结果预览**: 
  - 树形显示预期的整理结果
  - 标记匹配和未匹配的文件夹
  - 显示移动操作的详细信息

#### 2.1.4 确认执行模块
- **操作确认面板**:
  - 显示所有将要执行的移动操作
  - 标记源文件夹中没有匹配内容的情况
  - 提供操作取消选项
- **执行选项**:
  - 移动模式 vs 复制模式
  - 冲突处理策略 (覆盖/重命名/跳过)
  - 空文件夹处理 (保留/删除)

### 2.2 界面设计

#### 2.2.1 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│ 牛津教材分类整理器                                        │
├─────────────────────────────────────────────────────────┤
│ 1. 选择源文件夹                                          │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [拖拽区域] 或 [浏览按钮]                              │ │
│ │ [当前选择的源文件夹路径]                                  │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 2. 配置分类规则                                          │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 分类名称: [四年级    ] 关键词: [4A,4B        ] [添加] │ │
│ │ 分类名称: [五年级    ] 关键词: [5A,5B        ] [添加] │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 已配置规则列表                                    │ │ │
│ │ │ ☑ 四年级: 4A, 4B                    [编辑][删除] │ │ │
│ │ │ ☑ 五年级: 5A, 5B                    [编辑][删除] │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 3. 预览分析结果                                          │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [分析按钮]                                           │ │
│ │ ┌─────────────────────────────────────────────────┐ │ │
│ │ │ 预览树形结构                                      │ │ │
│ │ └─────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 4. 执行操作                                             │
│ │ [预览详情] [开始整理]                                  │ │
└─────────────────────────────────────────────────────────┘
```

#### 2.2.2 确认面板设计
```
┌─────────────────────────────────────────────────────────┐
│ 操作确认 - 即将执行的文件移动操作                          │
├─────────────────────────────────────────────────────────┤
│ 源文件夹: D:\BaiduNetdiskDownload\牛津新世代数学第三版     │
│ 目标文件夹: D:\BaiduNetdiskDownload\牛津新世代数学第三版-整理版本 │
│                                                         │
│ 移动操作详情:                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ✓ A\4A内容 → 四年级\A\                               │ │
│ │ ✓ A\5A内容 → 五年级\A\                               │ │
│ │ ✓ B\4B内容 → 四年级\B\                               │ │
│ │ ✓ B\5B内容 → 五年级\B\                               │ │
│ │ ⚠ C\ → 无匹配内容                                    │ │
│ │ ✓ D\包含4A的文件夹 → 四年级\D\                        │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 统计信息:                                               │
│ • 将移动 5 个文件夹                                      │
│ • 1 个源文件夹无匹配内容                                  │
│ • 预计创建目标结构: 2个年级分类, 4个子文件夹              │
│                                                         │
│ 选项:                                                   │
│ ○ 移动文件 ● 复制文件                                    │
│ ☑ 处理完成后删除空文件夹                                  │
│                                                         │
│ [取消] [确认执行]                                        │
└─────────────────────────────────────────────────────────┘
```

## 3. 技术实现

### 3.1 核心算法

#### 3.1.1 文件夹扫描算法
```python
def scan_folder_structure(source_path):
    """扫描源文件夹结构"""
    structure = {}
    for root, dirs, files in os.walk(source_path):
        rel_path = os.path.relpath(root, source_path)
        structure[rel_path] = {
            'dirs': dirs,
            'files': files,
            'full_path': root
        }
    return structure
```

#### 3.1.2 规则匹配算法
```python
def match_folder_to_category(folder_name, rules):
    """根据规则匹配文件夹到分类"""
    matches = []
    for category, keywords in rules.items():
        for keyword in keywords:
            if keyword.lower() in folder_name.lower():
                matches.append((category, keyword))
    return matches
```

#### 3.1.3 目标结构生成算法
```python
def generate_target_structure(source_structure, rules):
    """生成目标文件夹结构"""
    target_operations = []
    for folder_path, folder_info in source_structure.items():
        matches = match_folder_to_category(folder_path, rules)
        if matches:
            # 生成移动操作
            for category, keyword in matches:
                target_path = generate_target_path(folder_path, category)
                target_operations.append({
                    'source': folder_info['full_path'],
                    'target': target_path,
                    'category': category,
                    'matched_keyword': keyword
                })
    return target_operations
```

### 3.2 类设计

#### 3.2.1 主控制器类
```python
class OxfordOrganizer:
    def __init__(self, parent_frame, status_callback):
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        self.source_path = None
        self.rules = {}
        self.analysis_result = None
        
    def setup_ui(self):
        """设置用户界面"""
        pass
        
    def analyze_folder(self):
        """分析文件夹结构"""
        pass
        
    def execute_organization(self):
        """执行文件整理"""
        pass
```

#### 3.2.2 规则管理器类
```python
class RuleManager:
    def __init__(self):
        self.rules = {}
        
    def add_rule(self, category, keywords):
        """添加分类规则"""
        pass
        
    def remove_rule(self, category):
        """删除分类规则"""
        pass
        
    def export_rules(self, file_path):
        """导出规则到文件"""
        pass
        
    def import_rules(self, file_path):
        """从文件导入规则"""
        pass
```

### 3.3 集成到现有架构

#### 3.3.1 添加到主程序
在 `main.py` 中添加新的标签页:
```python
# 初始化牛津教材整理器
self.oxford_organizer = OxfordOrganizer(self.root, update_status)
self.oxford_organizer.setup_oxford_tab(self.notebook)
```

#### 3.3.2 处理系统拖拽
在 `on_system_drop` 方法中添加新的处理分支:
```python
elif tab_text == "牛津教材整理":
    self.oxford_organizer.handle_system_drop(files)
```

## 4. 用户体验设计

### 4.1 操作流程
1. **拖拽输入**: 用户拖拽源文件夹到界面
2. **规则配置**: 用户配置分类规则 (可保存常用规则)
3. **预览分析**: 点击分析按钮，显示预期结果
4. **确认执行**: 查看详细操作列表，确认后执行

### 4.2 错误处理
- **路径不存在**: 提示用户重新选择
- **权限不足**: 提示管理员权限需求
- **磁盘空间不足**: 提前检查并警告
- **文件占用**: 提供重试机制

### 4.3 进度反馈
- **分析进度**: 显示扫描进度条
- **执行进度**: 显示文件移动进度
- **完成提示**: 显示操作结果统计

## 5. 扩展性考虑

### 5.1 规则引擎扩展
- 支持更复杂的匹配规则 (正则表达式)
- 支持条件组合 (AND, OR, NOT)
- 支持自定义脚本规则

### 5.2 模板系统
- 预定义常用教材整理模板
- 用户自定义模板保存/分享
- 模板导入/导出功能

### 5.3 批量处理
- 支持多个源文件夹同时处理
- 支持批量应用相同规则
- 支持定时自动整理

这个设计文档提供了完整的需求分析和技术实现方案，可以作为开发的指导文档。
