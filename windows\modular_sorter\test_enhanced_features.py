"""
测试增强版功能
"""
import os
import tempfile
from pathlib import Path
from enhanced_rule_manager import EnhancedRuleManager
from folder_analyzer import FolderAnalyzer


def create_test_structure():
    """创建测试文件夹结构"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="enhanced_test_")
    base_path = Path(temp_dir) / "测试文件夹"
    base_path.mkdir(parents=True)
    
    # 创建测试文件夹结构
    test_folders = [
        "中四/4A相关内容",
        "中四/4B相关内容", 
        "中五/5A相关内容",
        "中五/5B相关内容",
        "其他/无关内容",
        "练习册/4A练习",
        "练习册/5B练习",
        "教师用书/4A教师版",
        "数学/基础数学",
        "英语/基础英语"
    ]
    
    for folder_path in test_folders:
        folder = base_path / folder_path
        folder.mkdir(parents=True, exist_ok=True)
        
        # 创建一些测试文件
        for i in range(3):
            test_file = folder / f"测试文件{i+1}.txt"
            test_file.write_text(f"这是测试文件 {i+1}", encoding='utf-8')
    
    print(f"创建测试结构: {base_path}")
    return str(base_path)


def test_enhanced_rule_manager():
    """测试增强版规则管理器"""
    print("\n=== 测试增强版规则管理器 ===")
    
    rm = EnhancedRuleManager()
    
    # 显示默认规则统计
    stats = rm.get_statistics()
    print(f"规则统计: 总计{stats['total_rules']}个规则，启用{stats['enabled_rules']}个，禁用{stats['disabled_rules']}个")
    
    # 显示启用的规则
    enabled_rules = rm.get_enabled_rules()
    print(f"启用的规则: {list(enabled_rules.keys())}")
    
    # 测试启用/禁用功能
    print("\n--- 测试启用/禁用功能 ---")
    rm.enable_rule("小一", True)
    rm.enable_rule("小二", True)
    rm.enable_rule("练习册", True)
    rm.enable_rule("教师用书", True)
    rm.enable_rule("数学", True)
    
    enabled_rules = rm.get_enabled_rules()
    print(f"修改后启用的规则: {list(enabled_rules.keys())}")
    
    # 测试匹配功能
    print("\n--- 测试匹配功能 ---")
    test_folders = ["4A相关内容", "5B练习", "教师版答案", "数学基础", "无关内容"]
    for folder in test_folders:
        matches = rm.match_folder_to_categories(folder)
        if matches:
            print(f"'{folder}' 匹配到: {matches}")
        else:
            print(f"'{folder}' 无匹配")
    
    return rm


def test_folder_analysis_with_enhanced_rules():
    """测试使用增强版规则的文件夹分析"""
    print("\n=== 测试文件夹分析（增强版规则） ===")
    
    # 创建测试结构
    source_path = create_test_structure()
    
    # 创建增强版规则管理器
    rm = test_enhanced_rule_manager()
    
    # 创建文件夹分析器
    fa = FolderAnalyzer(rm)
    fa.set_source_path(source_path)
    target_base_path = str(Path(source_path).parent / "整理后")
    
    # 扫描文件夹
    fa.scan_folder_structure()
    print(f"扫描到 {len(fa.folder_structure)} 个文件夹")
    
    # 查找匹配
    matches = fa.analyze_matches()
    print(f"匹配到 {len(matches)} 个项目:")
    for match in matches:
        item_type = "文件夹" if match.item_info.is_dir else "文件"
        print(f"  - {match.item_info.name} ({item_type}) → {match.category} (关键词: {match.matched_keyword})")
    
    # 显示未匹配的文件夹
    unmatched = fa.get_unmatched_folders()
    print(f"未匹配的文件夹 ({len(unmatched)}个):")
    for folder in unmatched:
        print(f"  - {folder.name}")
    
    # 生成移动操作
    operations = fa.generate_move_operations(target_base_path)
    print(f"生成 {len(operations)} 个移动操作")

    # 显示详细的移动操作路径
    print("详细移动操作:")
    for i, op in enumerate(operations, 1):
        source_rel = Path(op.source_path).relative_to(source_path)
        target_rel = Path(op.target_path).relative_to(target_base_path)
        print(f"  {i:2d}. {source_rel} → {target_rel}")
    
    # 获取分析摘要
    summary = fa.get_analysis_summary()
    print(f"分析摘要: {summary}")
    
    print(f"\n测试文件夹位置: {source_path}")
    print("可以手动检查生成的文件夹结构")


def test_rule_management_features():
    """测试规则管理功能"""
    print("\n=== 测试规则管理功能 ===")
    
    rm = EnhancedRuleManager()
    
    # 测试添加自定义规则
    print("--- 添加自定义规则 ---")
    rm.add_rule("测试分类", ["测试1", "测试2"], enabled=True)
    
    # 测试导出规则
    print("--- 测试导出规则 ---")
    export_path = "test_rules_export.json"
    if rm.export_rules(export_path):
        print(f"规则已导出到: {export_path}")
    
    # 测试重置为默认规则
    print("--- 测试重置功能 ---")
    original_count = len(rm.get_all_rules())
    rm.reset_to_defaults()
    new_count = len(rm.get_all_rules())
    print(f"重置前: {original_count} 个规则，重置后: {new_count} 个规则")
    
    # 测试导入规则
    print("--- 测试导入规则 ---")
    if os.path.exists(export_path):
        if rm.import_rules(export_path):
            print("规则导入成功")
        os.remove(export_path)  # 清理测试文件
    
    # 最终统计
    final_stats = rm.get_statistics()
    print(f"最终统计: {final_stats}")


if __name__ == "__main__":
    print("开始测试增强版功能...")
    
    try:
        test_enhanced_rule_manager()
        test_folder_analysis_with_enhanced_rules()
        test_rule_management_features()
        
        print("\n=== 测试完成 ===")
        print("所有功能测试通过！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
