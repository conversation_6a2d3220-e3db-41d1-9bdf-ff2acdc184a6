"""
牛津教材分类整理器 - 主管理器类
智能文件分类整理工具，根据用户定义的规则自动整理教材文件夹结构
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinterdnd2 import DND_FILES
from pathlib import Path
from typing import Optional, List

from enhanced_rule_manager import EnhancedRuleManager
from folder_analyzer import FolderAnalyzer
from operation_executor import OperationExecutor, OperationType, ConflictStrategy


class OxfordOrganizer:
    """牛津教材分类整理器主管理器"""
    
    def __init__(self, parent_frame, status_callback):
        """
        初始化整理器
        
        Args:
            parent_frame: 父窗口框架
            status_callback: 状态更新回调函数
        """
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        
        # 初始化组件
        self.rule_manager = EnhancedRuleManager()
        self.folder_analyzer = FolderAnalyzer(self.rule_manager)
        self.operation_executor = OperationExecutor(self._progress_callback)
        
        # UI组件
        self.tab_frame = None
        self.source_path_var = tk.StringVar()
        self.rule_listbox = None
        self.category_entry = None
        self.keywords_entry = None
        self.preview_tree = None
        self.operation_type_var = tk.StringVar(value="move")
        self.delete_empty_var = tk.BooleanVar(value=True)
        
        # 数据
        self.current_operations = []
        self.analysis_summary = {}
    
    def setup_oxford_tab(self, notebook):
        """设置牛津教材整理标签页"""
        # 创建标签页
        self.tab_frame = ttk.Frame(notebook)
        notebook.add(self.tab_frame, text="牛津教材整理")
        
        # 创建主要布局
        self._setup_main_layout()
        
        # 设置拖拽功能
        self._setup_drag_drop()
    
    def _setup_main_layout(self):
        """设置主要布局"""
        # 创建滚动框架
        canvas = tk.Canvas(self.tab_frame)
        scrollbar = ttk.Scrollbar(self.tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 1. 源文件夹选择区域
        self._setup_source_selection(scrollable_frame)
        
        # 2. 规则配置区域
        self._setup_rule_configuration(scrollable_frame)
        
        # 3. 预览分析区域
        self._setup_preview_analysis(scrollable_frame)
        
        # 4. 执行操作区域
        self._setup_execution_controls(scrollable_frame)
    
    def _setup_source_selection(self, parent):
        """设置源文件夹选择区域"""
        frame = ttk.LabelFrame(parent, text="1. 选择源文件夹", padding=10)
        frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 拖拽区域
        drag_frame = tk.Frame(frame, bg="lightblue", height=80, relief=tk.SUNKEN, bd=2)
        drag_frame.pack(fill=tk.X, pady=(0, 10))
        
        drag_label = tk.Label(drag_frame, text="拖拽文件夹到此处 或 点击浏览按钮选择",
                             bg="lightblue", font=("Arial", 10))
        drag_label.pack(expand=True)
        
        # 路径显示和浏览按钮
        path_frame = ttk.Frame(frame)
        path_frame.pack(fill=tk.X)
        
        ttk.Label(path_frame, text="当前路径:").pack(side=tk.LEFT)
        path_label = ttk.Label(path_frame, textvariable=self.source_path_var, 
                              foreground="blue", font=("Arial", 9))
        path_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        ttk.Button(path_frame, text="浏览", command=self._browse_source_folder).pack(side=tk.RIGHT)
        
        # 保存拖拽区域引用
        self.drag_area = drag_frame
    
    def _setup_rule_configuration(self, parent):
        """设置规则配置区域"""
        frame = ttk.LabelFrame(parent, text="2. 配置分类规则", padding=10)
        frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 规则添加区域
        add_frame = ttk.Frame(frame)
        add_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(add_frame, text="分类名称:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.category_entry = ttk.Entry(add_frame, width=15)
        self.category_entry.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(add_frame, text="关键词(逗号分隔):").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.keywords_entry = ttk.Entry(add_frame, width=20)
        self.keywords_entry.grid(row=0, column=3, padx=(0, 10))
        
        ttk.Button(add_frame, text="添加规则", command=self._add_rule).grid(row=0, column=4)
        
        # 规则列表区域
        list_frame = ttk.Frame(frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview来显示规则和启用状态
        columns = ("enabled", "keywords")
        self.rule_tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=8)

        # 设置列标题
        self.rule_tree.heading("#0", text="分类名称")
        self.rule_tree.heading("enabled", text="状态")
        self.rule_tree.heading("keywords", text="关键词")

        # 设置列宽
        self.rule_tree.column("#0", width=120)
        self.rule_tree.column("enabled", width=60)
        self.rule_tree.column("keywords", width=200)

        self.rule_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        rule_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.rule_tree.yview)
        rule_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.rule_tree.config(yscrollcommand=rule_scrollbar.set)

        # 绑定双击事件来切换启用状态
        self.rule_tree.bind("<Double-1>", self._toggle_rule_enabled)
        
        # 规则操作按钮
        rule_btn_frame = ttk.Frame(frame)
        rule_btn_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(rule_btn_frame, text="添加规则", command=self._add_rule).pack(side=tk.LEFT)
        ttk.Button(rule_btn_frame, text="编辑规则", command=self._edit_rule).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(rule_btn_frame, text="删除规则", command=self._delete_rule).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(rule_btn_frame, text="启用/禁用", command=self._toggle_selected_rule).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(rule_btn_frame, text="导入规则", command=self._import_rules).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(rule_btn_frame, text="导出规则", command=self._export_rules).pack(side=tk.LEFT, padx=(5, 0))
        
        # 刷新规则列表
        self._refresh_rule_list()
    
    def _setup_preview_analysis(self, parent):
        """设置预览分析区域"""
        frame = ttk.LabelFrame(parent, text="3. 预览分析结果", padding=10)
        frame.pack(fill=tk.X, padx=10, pady=5)

        # 分析按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(btn_frame, text="分析文件夹", command=self._analyze_folder,
                  style="Accent.TButton").pack(side=tk.LEFT)
        ttk.Button(btn_frame, text="查看详细预览", command=self._show_detailed_preview,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(10, 0))

        # 简要统计信息显示
        self.summary_frame = ttk.Frame(frame)
        self.summary_frame.pack(fill=tk.X, pady=(10, 0))

        self.summary_label = tk.Label(self.summary_frame, text="请先分析文件夹",
                                    font=("Arial", 10), fg="gray")
        self.summary_label.pack()
    
    def _setup_execution_controls(self, parent):
        """设置执行操作区域"""
        frame = ttk.LabelFrame(parent, text="4. 执行操作", padding=10)
        frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 操作选项
        options_frame = ttk.Frame(frame)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 操作类型
        ttk.Label(options_frame, text="操作类型:").pack(side=tk.LEFT)
        ttk.Radiobutton(options_frame, text="移动文件", variable=self.operation_type_var, 
                       value="move").pack(side=tk.LEFT, padx=(5, 10))
        ttk.Radiobutton(options_frame, text="复制文件", variable=self.operation_type_var, 
                       value="copy").pack(side=tk.LEFT, padx=(0, 10))
        
        # 其他选项
        ttk.Checkbutton(options_frame, text="删除空文件夹", 
                       variable=self.delete_empty_var).pack(side=tk.LEFT, padx=(10, 0))
        
        # 执行按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X)
        
        ttk.Button(btn_frame, text="预览详情", command=self._preview_operations).pack(side=tk.LEFT)
        ttk.Button(btn_frame, text="开始整理", command=self._execute_operations,
                  style="Accent.TButton").pack(side=tk.RIGHT)
    
    def _setup_drag_drop(self):
        """设置拖拽功能"""
        self.drag_area.drop_target_register(DND_FILES)
        self.drag_area.dnd_bind('<<Drop>>', self._on_folder_drop)
    
    def handle_system_drop(self, files):
        """处理系统拖拽事件"""
        if files:
            folder_path = files[0]  # 取第一个文件/文件夹
            path_obj = Path(folder_path)
            if path_obj.exists() and path_obj.is_dir():
                self.source_path_var.set(folder_path)
                self.folder_analyzer.set_source_path(folder_path)
                self.status_callback(f"已选择源文件夹: {path_obj.name}")
    
    def on_f5_refresh(self):
        """F5刷新处理"""
        self._refresh_rule_list()
        if self.source_path_var.get():
            self._analyze_folder()
        self.status_callback("已刷新牛津教材整理器")
    
    def _on_folder_drop(self, event):
        """处理文件夹拖拽事件"""
        try:
            root = self.parent_frame.winfo_toplevel()
            files = root.tk.splitlist(event.data)
            self.handle_system_drop(files)
        except Exception as e:
            self.status_callback(f"拖拽失败: {str(e)}", "lightcoral")
    
    def _browse_source_folder(self):
        """浏览选择源文件夹"""
        folder_path = filedialog.askdirectory(title="选择要整理的源文件夹")
        if folder_path:
            self.source_path_var.set(folder_path)
            self.folder_analyzer.set_source_path(folder_path)
            self.status_callback(f"已选择源文件夹: {Path(folder_path).name}")
    
    def _add_rule(self):
        """添加分类规则"""
        category = self.category_entry.get().strip()
        keywords_text = self.keywords_entry.get().strip()
        
        if not category or not keywords_text:
            messagebox.showwarning("警告", "请输入分类名称和关键词")
            return
        
        keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
        if not keywords:
            messagebox.showwarning("警告", "请输入有效的关键词")
            return
        
        if self.rule_manager.add_rule(category, keywords):
            self._refresh_rule_list()
            self.category_entry.delete(0, tk.END)
            self.keywords_entry.delete(0, tk.END)
            self.status_callback(f"已添加规则: {category}")
        else:
            messagebox.showerror("错误", "添加规则失败")

    def _edit_rule(self):
        """编辑选中的规则"""
        item = self.rule_tree.selection()[0] if self.rule_tree.selection() else None
        if not item:
            messagebox.showwarning("警告", "请选择要编辑的规则")
            return

        category = self.rule_tree.item(item, "text")
        rules = self.rule_manager.get_all_rules()
        if category not in rules:
            messagebox.showerror("错误", "规则不存在")
            return

        current_keywords = rules[category]["keywords"]
        current_enabled = rules[category]["enabled"]

        # 创建编辑对话框
        self._show_edit_rule_dialog(category, current_keywords, current_enabled)

    def _show_edit_rule_dialog(self, category, keywords, enabled):
        """显示编辑规则对话框"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("编辑规则")
        dialog.geometry("500x300")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 分类名称
        ttk.Label(dialog, text="分类名称:").pack(pady=(10, 5))
        category_var = tk.StringVar(value=category)
        category_entry = ttk.Entry(dialog, textvariable=category_var, width=40)
        category_entry.pack(pady=(0, 10))

        # 关键词
        ttk.Label(dialog, text="关键词 (用逗号分隔):").pack(pady=(0, 5))
        keywords_var = tk.StringVar(value=", ".join(keywords))
        keywords_text = tk.Text(dialog, height=8, width=50)
        keywords_text.pack(pady=(0, 10), padx=10, fill=tk.BOTH, expand=True)
        keywords_text.insert("1.0", keywords_var.get())

        # 启用状态
        enabled_var = tk.BooleanVar(value=enabled)
        ttk.Checkbutton(dialog, text="启用此规则", variable=enabled_var).pack(pady=(0, 10))

        # 按钮
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(pady=10)

        def save_changes():
            new_category = category_var.get().strip()
            new_keywords_text = keywords_text.get("1.0", tk.END).strip()
            new_enabled = enabled_var.get()

            if not new_category or not new_keywords_text:
                messagebox.showwarning("警告", "请输入分类名称和关键词")
                return

            new_keywords = [kw.strip() for kw in new_keywords_text.split(',') if kw.strip()]
            if not new_keywords:
                messagebox.showwarning("警告", "请输入有效的关键词")
                return

            # 如果分类名称改变了，需要删除旧的并添加新的
            if new_category != category:
                if new_category in self.rule_manager.get_all_rules():
                    messagebox.showerror("错误", f"分类 '{new_category}' 已存在")
                    return
                self.rule_manager.remove_rule(category)
                self.rule_manager.add_rule(new_category, new_keywords, enabled=new_enabled)
            else:
                # 更新现有规则
                self.rule_manager.rules[category]["keywords"] = new_keywords
                self.rule_manager.rules[category]["enabled"] = new_enabled
                self.rule_manager.save_rules()

            self._refresh_rule_list()
            self.status_callback(f"已更新规则: {new_category}")
            dialog.destroy()

        ttk.Button(btn_frame, text="保存", command=save_changes).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

    def _delete_rule(self):
        """删除选中的规则"""
        item = self.rule_tree.selection()[0] if self.rule_tree.selection() else None
        if not item:
            messagebox.showwarning("警告", "请选择要删除的规则")
            return

        category = self.rule_tree.item(item, "text")

        if messagebox.askyesno("确认", f"确定要删除规则 '{category}' 吗？"):
            if self.rule_manager.remove_rule(category):
                self._refresh_rule_list()
                self.status_callback(f"已删除规则: {category}")
    
    def _import_rules(self):
        """导入规则"""
        file_path = filedialog.askopenfilename(
            title="选择规则文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            if self.rule_manager.import_rules(file_path):
                self._refresh_rule_list()
                self.status_callback("规则导入成功")
            else:
                messagebox.showerror("错误", "规则导入失败")
    
    def _export_rules(self):
        """导出规则"""
        file_path = filedialog.asksaveasfilename(
            title="保存规则文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            if self.rule_manager.export_rules(file_path):
                self.status_callback("规则导出成功")
            else:
                messagebox.showerror("错误", "规则导出失败")
    
    def _refresh_rule_list(self):
        """刷新规则列表"""
        # 清空树
        for item in self.rule_tree.get_children():
            self.rule_tree.delete(item)

        # 获取所有规则
        rules = self.rule_manager.get_all_rules()

        # 添加规则到树中
        for category, rule_data in rules.items():
            keywords = rule_data.get("keywords", [])
            enabled = rule_data.get("enabled", True)

            status = "✅ 启用" if enabled else "❌ 禁用"
            keywords_text = ", ".join(keywords[:3])  # 只显示前3个关键词
            if len(keywords) > 3:
                keywords_text += f"... (+{len(keywords)-3})"

            self.rule_tree.insert("", "end", text=category,
                                values=(status, keywords_text))

    def _toggle_rule_enabled(self, event):
        """双击切换规则启用状态"""
        item = self.rule_tree.selection()[0] if self.rule_tree.selection() else None
        if item:
            category = self.rule_tree.item(item, "text")
            self._toggle_rule_status(category)

    def _toggle_selected_rule(self):
        """切换选中规则的启用状态"""
        item = self.rule_tree.selection()[0] if self.rule_tree.selection() else None
        if not item:
            messagebox.showwarning("警告", "请先选择一个规则")
            return

        category = self.rule_tree.item(item, "text")
        self._toggle_rule_status(category)

    def _toggle_rule_status(self, category):
        """切换规则状态"""
        rules = self.rule_manager.get_all_rules()
        if category in rules:
            current_status = rules[category].get("enabled", True)
            self.rule_manager.enable_rule(category, not current_status)
            self._refresh_rule_list()
            status_text = "禁用" if current_status else "启用"
            self.status_callback(f"规则 '{category}' 已{status_text}")

    def _analyze_folder(self):
        """分析文件夹结构"""
        if not self.source_path_var.get():
            messagebox.showwarning("警告", "请先选择源文件夹")
            return

        # 验证规则
        errors = self.rule_manager.validate_rules()
        if errors:
            messagebox.showerror("规则错误", "\n".join(errors))
            return

        try:
            # 执行分析
            self.status_callback("正在分析文件夹结构...")

            # 扫描文件夹结构
            self.folder_analyzer.scan_folder_structure()

            # 获取分析摘要
            self.analysis_summary = self.folder_analyzer.get_analysis_summary()

            # 生成移动操作
            self.current_operations = self.folder_analyzer.generate_move_operations()

            # 更新简要统计信息
            self._update_summary_display()

            # 显示分析结果
            total = self.analysis_summary['total_folders']
            matched = self.analysis_summary['matched_folders']
            unmatched = self.analysis_summary['unmatched_folders']

            self.status_callback(f"分析完成: 共{total}个文件夹，匹配{matched}个，未匹配{unmatched}个")

        except Exception as e:
            messagebox.showerror("分析错误", f"分析文件夹时出错: {str(e)}")
            self.status_callback("分析失败", "lightcoral")

    def _update_summary_display(self):
        """更新简要统计信息显示"""
        if not self.analysis_summary:
            self.summary_label.config(text="请先分析文件夹", fg="gray")
            return

        total = self.analysis_summary['total_folders']
        matched = self.analysis_summary['matched_folders']
        unmatched = self.analysis_summary['unmatched_folders']
        categories = self.analysis_summary['categories']

        summary_text = f"📊 分析结果: 共 {total} 个文件夹 | ✅ 匹配 {matched} 个 | ❌ 未匹配 {unmatched} 个 | 📁 分类 {categories} 个"
        self.summary_label.config(text=summary_text, fg="blue")

    def _show_detailed_preview(self):
        """显示详细预览窗口"""
        if not self.current_operations:
            messagebox.showwarning("警告", "请先分析文件夹")
            return

        # 创建详细预览窗口
        preview_window = tk.Toplevel(self.parent_frame)
        preview_window.title("详细预览 - 文件夹分类结果")
        preview_window.geometry("1200x800")
        preview_window.transient(self.parent_frame.winfo_toplevel())

        # 居中显示窗口
        preview_window.update_idletasks()
        x = (preview_window.winfo_screenwidth() // 2) - (1200 // 2)
        y = (preview_window.winfo_screenheight() // 2) - (800 // 2)
        preview_window.geometry(f"1200x800+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建详细树形控件
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview
        columns = ("source_path", "target_path", "category", "matched_keyword", "size", "files")
        detail_tree = ttk.Treeview(tree_frame, columns=columns, show="tree headings", height=25)

        # 设置列标题
        detail_tree.heading("#0", text="文件夹结构")
        detail_tree.heading("source_path", text="源路径")
        detail_tree.heading("target_path", text="目标路径")
        detail_tree.heading("category", text="分类")
        detail_tree.heading("matched_keyword", text="匹配关键词")
        detail_tree.heading("size", text="大小")
        detail_tree.heading("files", text="文件数")

        # 设置列宽
        detail_tree.column("#0", width=250)
        detail_tree.column("source_path", width=200)
        detail_tree.column("target_path", width=200)
        detail_tree.column("category", width=100)
        detail_tree.column("matched_keyword", width=100)
        detail_tree.column("size", width=80)
        detail_tree.column("files", width=60)

        # 添加滚动条
        tree_scrollbar_y = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=detail_tree.yview)
        tree_scrollbar_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=detail_tree.xview)

        detail_tree.configure(yscrollcommand=tree_scrollbar_y.set, xscrollcommand=tree_scrollbar_x.set)

        # 布局
        detail_tree.grid(row=0, column=0, sticky="nsew")
        tree_scrollbar_y.grid(row=0, column=1, sticky="ns")
        tree_scrollbar_x.grid(row=1, column=0, sticky="ew")

        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # 填充树形数据
        self._populate_detailed_tree(detail_tree)

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(btn_frame, text="关闭", command=preview_window.destroy).pack(side=tk.RIGHT)
        ttk.Button(btn_frame, text="导出预览", command=lambda: self._export_preview_report()).pack(side=tk.RIGHT, padx=(0, 10))

    def _populate_detailed_tree(self, tree):
        """填充详细树形数据"""
        # 清空树
        for item in tree.get_children():
            tree.delete(item)

        if not self.current_operations:
            return

        source_path = self.source_path_var.get()
        target_base = self.folder_analyzer._generate_target_base_path()

        # 按分类组织数据
        categories = {}
        for op in self.current_operations:
            category = op.category
            if category not in categories:
                categories[category] = []
            categories[category].append(op)

        # 添加分类节点
        for category, operations in categories.items():
            # 计算分类统计
            total_size = sum(op.item_info.size for op in operations)
            total_files = sum(op.item_info.file_count for op in operations)

            size_str = self._format_size(total_size)

            # 添加分类节点
            category_node = tree.insert("", "end", text=f"📁 {category} ({len(operations)}个文件夹)",
                                      values=("", f"{target_base}\\{category}", "", "", size_str, str(total_files)))

            # 添加文件夹节点
            for op in operations:
                source_rel = str(Path(op.source_path).relative_to(source_path))
                target_rel = str(Path(op.target_path).relative_to(target_base))
                item_size = self._format_size(op.item_info.size)
                item_type = "📂" if op.item_info.is_dir else "📄"
                file_count_str = str(op.item_info.file_count) if op.item_info.is_dir else "1"

                tree.insert(category_node, "end",
                          text=f"{item_type} {op.item_info.name}",
                          values=(source_rel, target_rel, op.category, op.matched_keyword,
                                item_size, file_count_str))

        # 展开所有节点
        for item in tree.get_children():
            tree.item(item, open=True)

        # 添加未匹配的文件夹
        unmatched = self.folder_analyzer.get_unmatched_folders()
        if unmatched:
            unmatched_node = tree.insert("", "end", text=f"❌ 未匹配 ({len(unmatched)}个文件夹)",
                                       values=("", "不会移动", "", "", "", ""))
            for folder in unmatched:
                rel_path = str(Path(folder.path).relative_to(source_path))
                folder_size = self._format_size(folder.size)
                tree.insert(unmatched_node, "end",
                          text=f"📂 {folder.name}",
                          values=(rel_path, "不会移动", "无匹配", "", folder_size, str(folder.file_count)))
            tree.item(unmatched_node, open=True)

    def _export_preview_report(self):
        """导出预览报告"""
        if not self.current_operations:
            messagebox.showwarning("警告", "没有可导出的预览数据")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存预览报告",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                content = self._generate_preview_content()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"预览报告已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存报告失败: {str(e)}")

    def _generate_preview_content(self) -> str:
        """生成预览报告内容"""
        if not self.current_operations or not self.analysis_summary:
            return "没有可用的预览数据"

        content = []
        content.append("=" * 60)
        content.append("牛津教材分类整理 - 预览报告")
        content.append("=" * 60)
        content.append("")

        # 基本信息
        content.append("📊 分析摘要:")
        content.append(f"  • 源文件夹: {self.source_path_var.get()}")
        content.append(f"  • 目标文件夹: {self.target_path_var.get()}")
        content.append(f"  • 总文件夹数: {self.analysis_summary['total_folders']}")
        content.append(f"  • 匹配文件夹数: {self.analysis_summary['matched_folders']}")
        content.append(f"  • 未匹配文件夹数: {self.analysis_summary['unmatched_folders']}")
        content.append(f"  • 分类数量: {self.analysis_summary['categories']}")
        content.append("")

        # 分类详情
        content.append("📁 分类详情:")
        for category, stats in self.analysis_summary['category_stats'].items():
            content.append(f"  {category}:")
            content.append(f"    - 文件夹数: {stats['count']}")
            content.append(f"    - 总大小: {self._format_size(stats['total_size'])}")
            content.append(f"    - 文件夹列表: {', '.join(stats['folders'])}")
        content.append("")

        # 移动操作详情
        content.append("🔄 移动操作详情:")
        source_path = self.source_path_var.get()
        target_base = self.folder_analyzer._generate_target_base_path()

        for i, op in enumerate(self.current_operations, 1):
            source_rel = str(Path(op.source_path).relative_to(source_path))
            target_rel = str(Path(op.target_path).relative_to(target_base))
            content.append(f"  {i:2d}. {source_rel}")
            content.append(f"      → {target_rel}")
            content.append(f"      分类: {op.category} | 关键词: {op.matched_keyword}")
            item_type = "文件夹" if op.item_info.is_dir else "文件"
            file_count_info = f"文件数: {op.item_info.file_count}" if op.item_info.is_dir else "文件"
            content.append(f"      类型: {item_type} | 大小: {self._format_size(op.item_info.size)} | {file_count_info}")
            content.append("")

        # 未匹配文件夹
        unmatched = self.folder_analyzer.get_unmatched_folders()
        if unmatched:
            content.append("❌ 未匹配文件夹:")
            for folder in unmatched:
                rel_path = str(Path(folder.path).relative_to(source_path))
                content.append(f"  • {rel_path}")
                content.append(f"    大小: {self._format_size(folder.size)} | 文件数: {folder.file_count}")
            content.append("")

        content.append("=" * 60)
        content.append("报告生成完成")
        content.append("=" * 60)

        return "\n".join(content)

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"

        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)

        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1

        if unit_index == 0:
            return f"{int(size)}{units[unit_index]}"
        else:
            return f"{size:.1f}{units[unit_index]}"

    def _preview_operations(self):
        """预览操作详情"""
        if not self.current_operations:
            messagebox.showwarning("警告", "请先分析文件夹")
            return

        # 创建预览窗口 - 增加宽度以更好地显示内容
        preview_window = tk.Toplevel(self.parent_frame)
        preview_window.title("操作预览详情")
        preview_window.geometry("1000x700")
        preview_window.transient(self.parent_frame.winfo_toplevel())

        # 居中显示窗口
        preview_window.update_idletasks()
        x = (preview_window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (preview_window.winfo_screenheight() // 2) - (700 // 2)
        preview_window.geometry(f"1000x700+{x}+{y}")

        # 创建文本框显示详情
        text_frame = ttk.Frame(preview_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 生成预览内容
        content = self._generate_preview_content()
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)

        # 按钮框架
        btn_frame = ttk.Frame(preview_window)
        btn_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(btn_frame, text="关闭", command=preview_window.destroy).pack(side=tk.RIGHT)

    def _generate_preview_content(self) -> str:
        """生成预览内容"""
        if not self.current_operations:
            return "没有要执行的操作"

        content = []
        content.append("=" * 80)
        content.append("操作预览详情")
        content.append("=" * 80)
        content.append("")

        # 基本信息
        source_path = self.source_path_var.get()
        target_base = self.folder_analyzer._generate_target_base_path()

        content.append(f"源文件夹: {source_path}")
        content.append(f"目标文件夹: {target_base}")
        content.append(f"操作类型: {'移动' if self.operation_type_var.get() == 'move' else '复制'}")
        content.append(f"删除空文件夹: {'是' if self.delete_empty_var.get() else '否'}")
        content.append("")

        # 统计信息
        content.append("统计信息:")
        content.append(f"  总操作数: {len(self.current_operations)}")
        content.append(f"  涉及分类: {len(set(op.category for op in self.current_operations))}")

        total_size = sum(op.item_info.size for op in self.current_operations)
        content.append(f"  总大小: {self._format_size(total_size)}")
        content.append("")

        # 按分类显示操作
        categories = {}
        for op in self.current_operations:
            if op.category not in categories:
                categories[op.category] = []
            categories[op.category].append(op)

        for category, operations in categories.items():
            content.append(f"【{category}】({len(operations)}个文件夹)")
            content.append("-" * 60)

            for i, op in enumerate(operations, 1):
                source_rel = Path(op.source_path).relative_to(source_path)
                target_rel = Path(op.target_path).relative_to(target_base)

                content.append(f"  {i:2d}. {source_rel} → {target_rel}")
                content.append(f"      匹配关键词: {op.matched_keyword}")
                item_type = "文件夹" if op.item_info.is_dir else "文件"
                file_count_info = f"文件数: {op.item_info.file_count}" if op.item_info.is_dir else "文件"
                content.append(f"      类型: {item_type}, 大小: {self._format_size(op.item_info.size)}, {file_count_info}")
                content.append("")

        # 未匹配的文件夹
        unmatched = self.folder_analyzer.get_unmatched_folders()
        if unmatched:
            content.append("【未匹配的文件夹】")
            content.append("-" * 60)
            for folder in unmatched:
                rel_path = Path(folder.path).relative_to(source_path)
                content.append(f"  ❌ {rel_path} (大小: {self._format_size(folder.size)})")
            content.append("")

        content.append("=" * 80)
        content.append("注意: 请仔细检查上述操作，确认无误后再执行整理操作！")
        content.append("=" * 80)

        return "\n".join(content)

    def _execute_operations(self):
        """执行整理操作"""
        if not self.current_operations:
            messagebox.showwarning("警告", "请先分析文件夹")
            return

        # 最终确认
        op_type = "移动" if self.operation_type_var.get() == "move" else "复制"
        total_ops = len(self.current_operations)

        if not messagebox.askyesno("确认执行",
                                 f"确定要{op_type} {total_ops} 个文件夹吗？\n\n"
                                 f"此操作{'不可撤销' if self.operation_type_var.get() == 'move' else '可能需要较长时间'}！"):
            return

        # 设置执行器参数
        if self.operation_type_var.get() == "move":
            self.operation_executor.set_operation_type(OperationType.MOVE)
        else:
            self.operation_executor.set_operation_type(OperationType.COPY)

        self.operation_executor.set_delete_empty_folders(self.delete_empty_var.get())
        self.operation_executor.set_conflict_strategy(ConflictStrategy.RENAME)

        # 验证操作
        validation_errors = self.operation_executor.validate_operations(self.current_operations)
        if validation_errors:
            messagebox.showerror("验证失败", "\n".join(validation_errors))
            return

        try:
            # 创建进度窗口
            self._create_progress_window()

            # 执行操作
            self.status_callback(f"正在{op_type}文件...")
            result = self.operation_executor.execute_operations(self.current_operations)

            # 关闭进度窗口
            if hasattr(self, 'progress_window'):
                self.progress_window.destroy()

            # 显示结果
            self._show_execution_result(result)

            # 清空当前操作
            if result.successful_operations > 0:
                self.current_operations = []
                self._update_summary_display()

        except Exception as e:
            if hasattr(self, 'progress_window'):
                self.progress_window.destroy()
            messagebox.showerror("执行错误", f"执行操作时出错: {str(e)}")
            self.status_callback("操作失败", "lightcoral")

    def _create_progress_window(self):
        """创建进度窗口"""
        self.progress_window = tk.Toplevel(self.parent_frame)
        self.progress_window.title("执行进度")
        self.progress_window.geometry("500x180")
        self.progress_window.transient(self.parent_frame.winfo_toplevel())
        self.progress_window.grab_set()

        # 居中显示
        self.progress_window.update_idletasks()
        x = (self.progress_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.progress_window.winfo_screenheight() // 2) - (180 // 2)
        self.progress_window.geometry(f"500x180+{x}+{y}")

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_window, variable=self.progress_var,
                                          maximum=100, length=450)
        self.progress_bar.pack(pady=20)

        # 状态标签
        self.progress_label = tk.Label(self.progress_window, text="准备开始...",
                                     font=("Arial", 10))
        self.progress_label.pack(pady=10)

        # 取消按钮（暂时不实现取消功能）
        # ttk.Button(self.progress_window, text="取消", command=self._cancel_operation).pack(pady=10)

    def _progress_callback(self, current: int, total: int, message: str):
        """进度回调函数"""
        if hasattr(self, 'progress_window') and self.progress_window.winfo_exists():
            progress = (current / total) * 100 if total > 0 else 0
            self.progress_var.set(progress)
            self.progress_label.config(text=f"{message} ({current}/{total})")
            self.progress_window.update()

    def _show_execution_result(self, result):
        """显示执行结果"""
        if result.failed_operations == 0:
            # 全部成功
            messagebox.showinfo("操作完成",
                              f"成功处理 {result.successful_operations} 个文件夹！\n\n"
                              f"目标位置: {self.folder_analyzer._generate_target_base_path()}")
            self.status_callback(f"整理完成，成功处理 {result.successful_operations} 个文件夹")
        else:
            # 部分失败
            error_text = "\n".join(result.errors[:5])  # 只显示前5个错误
            if len(result.errors) > 5:
                error_text += f"\n... 还有 {len(result.errors) - 5} 个错误"

            messagebox.showwarning("部分完成",
                                 f"成功处理: {result.successful_operations} 个\n"
                                 f"失败: {result.failed_operations} 个\n\n"
                                 f"错误详情:\n{error_text}")
            self.status_callback(f"整理完成，{result.failed_operations} 个失败", "orange")
