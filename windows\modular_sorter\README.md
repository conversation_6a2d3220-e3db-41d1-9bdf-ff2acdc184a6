# 模块化文件排序工具

这是原始 `file_folder_sorter_complete.py` 的模块化重构版本，解决了原有的UI问题并提供了更好的代码结构。

## 主要改进

### 🐛 修复的问题
1. **窗口默认置顶功能** - 恢复了窗口默认置顶功能，方便使用
2. **文件路径显示过长问题** - 在"待移动文件"列表中，现在只显示父目录名和文件名，而不是完整路径
3. **文件重新选中问题** - 移动文件到右侧后，新添加的文件会自动被选中，无需手动重新选择
4. **文件排序问题** - 拖拽文件到右侧列表时，自动按文件名排序（去除数字前缀）
5. **文件夹排序问题** - 修复了左侧文件夹树的排序问题，现在按名称正确排序（去除数字前缀）
6. **移动后刷新问题** - 修复了移动文件到文件夹后，右侧列表不刷新的问题

### 🆕 新增功能
1. **拖拽清空重载** - 拖入新的文件或文件夹时，自动清空之前的数据，加载最新数据
2. **F5刷新功能** - 支持F5键刷新当前标签页的文件/文件夹列表，自动移除不存在的项目
3. **自动合并移动** - 新增第五个标签页，支持将多个文件/文件夹合并移动到新文件夹中
4. **空文件夹清理** - 新增第六个标签页，扫描并清理文件夹中的所有空文件夹

### 🏗️ 架构改进
- **模块化设计** - 将原来929行的单一文件拆分为多个专门的模块
- **清晰的职责分离** - 每个模块负责特定的功能
- **更好的可维护性** - 代码更容易理解和修改
- **新增功能** - 添加了批量创建文件夹功能

## 文件结构

```
modular_sorter/
├── __init__.py              # 包初始化文件
├── main.py                  # 主程序入口
├── utils.py                 # 工具函数（排序、路径处理等）
├── drag_sort.py             # 拖拽排序功能管理器
├── batch_rename.py          # 批量重命名功能管理器
├── tree_manager.py          # 树形管理功能管理器
├── batch_folder_creator.py  # 批量创建文件夹功能管理器
├── auto_merge_mover.py      # 自动合并移动功能管理器
├── empty_folder_cleaner.py  # 空文件夹清理功能管理器
├── oxford_organizer.py      # 牛津教材分类整理器主管理器
├── rule_manager.py          # 分类规则管理器
├── folder_analyzer.py       # 文件夹分析器
├── operation_executor.py    # 操作执行器
└── README.md               # 说明文档
```

## 模块说明

### utils.py
- `natural_sort_key()` - 自然排序算法
- `remove_number_prefix()` - 去除数字前缀
- `get_display_name()` - 智能路径显示（解决路径过长问题）

### drag_sort.py
- `DragSortManager` - 处理文件/文件夹的拖拽排序功能
- 支持自动排序和手动拖拽调整顺序

### batch_rename.py
- `BatchRenameManager` - 处理批量文件重命名
- 支持剪贴板操作和文本编辑

### tree_manager.py
- `TreeManager` - 处理文件夹树形结构和文件移动
- **修复了路径显示问题** - 使用 `get_display_name()` 显示简化路径
- **修复了重新选中问题** - 自动选中新添加的文件
- **修复了文件排序问题** - 拖拽文件时自动按名称排序（去除数字前缀）
- **修复了文件夹排序问题** - 文件夹树按名称正确排序（去除数字前缀）
- **修复了移动后刷新问题** - 移动文件后自动刷新目标文件夹显示

### batch_folder_creator.py
- `BatchFolderCreator` - 处理批量创建文件夹功能
- 支持拖拽添加目标文件夹
- 支持多选目标文件夹
- 支持批量创建指定名称的子文件夹
- 提供预览功能，确认创建结果

### auto_merge_mover.py
- `AutoMergeMover` - 处理自动合并移动功能
- 支持将多个文件/文件夹合并到新文件夹中
- 智能生成合并文件夹名称
- 提供预览和确认功能

### empty_folder_cleaner.py
- `EmptyFolderCleaner` - 处理空文件夹清理功能
- 递归扫描和清理空文件夹
- 支持预览清理结果
- 提供安全的清理确认机制

### oxford_organizer.py
- `OxfordOrganizer` - 牛津教材分类整理器主管理器
- 智能文件分类整理工具，根据用户定义的规则自动整理教材文件夹结构
- 支持拖拽输入源文件夹
- 支持自定义分类规则配置
- 提供详细的预览分析功能
- 支持移动/复制操作选择

### rule_manager.py
- `RuleManager` - 分类规则管理器
- 处理分类规则的增删改查和持久化
- 支持规则导入/导出功能
- 提供规则验证和统计功能

### folder_analyzer.py
- `FolderAnalyzer` - 文件夹分析器
- 扫描和分析文件夹结构，生成移动操作计划
- 支持智能匹配和置信度计算
- 提供详细的分析摘要信息

### operation_executor.py
- `OperationExecutor` - 操作执行器
- 执行文件移动、复制等操作
- 支持冲突处理策略配置
- 提供进度回调和错误处理

### main.py
- `ModularFileFolderSorter` - 主应用程序类
- 协调各个管理器的工作
- 处理系统级拖拽事件
- 支持七个标签页：拖拽排序、批量重命名、树形管理、批量创建文件夹、自动合并移动、空文件夹清理、牛津教材整理

## 使用方法

### 方法1：直接运行启动脚本
```bash
python run_modular_sorter.py
```

### 方法2：进入模块目录运行
```bash
cd modular_sorter
python main.py
```

### 操作说明
1. 选择相应的标签页使用不同功能
2. 拖拽文件或文件夹到相应区域（会自动清空之前的数据）
3. 根据需要进行排序、重命名或移动操作
4. 按F5键可以刷新当前标签页的内容

### 快捷键
- **F5** - 刷新当前标签页的文件/文件夹列表，自动移除不存在的项目

### 拖拽行为
- **拖入新数据** - 自动清空之前的数据，加载最新拖入的文件/文件夹
- **智能排序** - 自动按文件名排序（去除数字前缀，如"01_"、"02_"等）

### 自动合并移动功能
- **拖入多个项目** - 支持同时拖入文件和文件夹
- **智能命名** - 自动生成合并文件夹名称：
  * 单个项目：去除数字前缀（如"01_"）和文件后缀（如".txt"）
  * 多个项目：用"+"连接所有项目名称（去除前缀和后缀）
- **预览功能** - 点击"预览结果"查看目标路径和文件夹名称
- **安全移动** - 如果目标文件夹已存在同名文件，自动添加序号避免冲突

### 空文件夹清理功能
- **递归扫描** - 深度扫描所有子文件夹，识别完全为空的文件夹
- **树形显示** - 以树形结构显示文件夹层次关系，空文件夹用红色标记
- **批量选择** - 支持全选空文件夹或手动选择特定文件夹
- **安全删除** - 删除前再次确认文件夹为空，避免误删除

## 功能特点

1. **窗口置顶** - 窗口默认置顶，方便操作
2. **拖拽排序** - 支持文件和文件夹的拖拽排序，自动添加序号
3. **批量重命名** - 支持批量编辑文件名，剪贴板操作
4. **树形管理** - 文件夹树形浏览，文件移动操作
5. **批量创建文件夹** - 在多个目标文件夹下批量创建指定名称的子文件夹
6. **自动合并移动** - 将多个文件/文件夹合并移动到新文件夹中，智能生成文件夹名称
7. **空文件夹清理** - 扫描文件夹中的所有空文件夹，以树形结构显示并支持批量删除
6. **智能路径显示** - 自动缩短过长的文件路径显示
7. **自动选中** - 新添加的文件自动被选中，提升用户体验
8. **智能排序** - 拖拽文件时自动按名称排序（去除数字前缀）
9. **自动刷新** - 移动文件后自动刷新目标文件夹显示
10. **拖拽清空重载** - 拖入新数据时自动清空之前的数据
11. **F5快速刷新** - 支持F5键快速刷新当前标签页内容
12. **智能合并命名** - 自动生成合并文件夹名称（单个项目去除序号后缀，多个项目用"+"连接）
13. **递归空文件夹扫描** - 深度扫描所有子文件夹，识别并标记空文件夹
14. **树形结构显示** - 以树形结构直观显示文件夹层次关系，空文件夹红色标记

## 与原版本的兼容性

- 保持了所有原有功能
- UI界面基本一致
- 操作方式完全相同
- 原始文件 `file_folder_sorter_complete.py` 保持不变

## 技术细节

- 使用 `tkinter` 和 `tkinterdnd2` 构建GUI
- 采用管理器模式组织代码
- 使用回调函数进行模块间通信
- 智能路径映射解决显示与存储分离问题
