"""
文件夹分析器 - 扫描和分析文件夹结构，生成移动操作计划
"""
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional, NamedTuple
from dataclasses import dataclass


@dataclass
class FolderInfo:
    """文件夹信息"""
    path: str
    name: str
    parent_path: str
    is_dir: bool
    size: int
    file_count: int
    subfolder_count: int


@dataclass
class FileInfo:
    """文件信息"""
    path: str
    name: str
    parent_path: str
    is_dir: bool
    size: int
    extension: str


@dataclass
class ItemInfo:
    """通用项目信息（文件或文件夹）"""
    path: str
    name: str
    parent_path: str
    is_dir: bool
    size: int
    # 文件夹特有属性
    file_count: int = 0
    subfolder_count: int = 0
    # 文件特有属性
    extension: str = ""


@dataclass
class MatchResult:
    """匹配结果"""
    item_info: ItemInfo  # 改为通用的ItemInfo，支持文件和文件夹
    category: str
    matched_keyword: str
    confidence: float = 1.0  # 匹配置信度


@dataclass
class MoveOperation:
    """移动操作"""
    source_path: str
    target_path: str
    category: str
    matched_keyword: str
    item_info: ItemInfo  # 改为通用的ItemInfo，支持文件和文件夹
    operation_type: str = "move"  # move, copy


class FolderAnalyzer:
    """文件夹和文件分析器"""

    def __init__(self, rule_manager):
        """
        初始化分析器

        Args:
            rule_manager: 规则管理器实例
        """
        self.rule_manager = rule_manager
        self.source_path = None
        self.folder_structure = {}  # 文件夹结构
        self.file_structure = {}    # 文件结构
        self.analysis_result = None
    
    def set_source_path(self, source_path: str) -> bool:
        """
        设置源文件夹路径
        
        Args:
            source_path: 源文件夹路径
            
        Returns:
            bool: 是否设置成功
        """
        path_obj = Path(source_path)
        if not path_obj.exists() or not path_obj.is_dir():
            return False
        
        self.source_path = str(path_obj.resolve())
        return True
    
    def scan_folder_structure(self) -> Dict[str, FolderInfo]:
        """
        扫描文件夹结构
        
        Returns:
            Dict[str, FolderInfo]: 文件夹结构字典，key为相对路径
        """
        if not self.source_path:
            return {}
        
        structure = {}
        source_path_obj = Path(self.source_path)
        
        try:
            # 递归扫描所有文件夹
            for root, dirs, files in os.walk(self.source_path):
                root_path = Path(root)
                
                # 计算相对路径
                try:
                    rel_path = root_path.relative_to(source_path_obj)
                    rel_path_str = str(rel_path) if rel_path != Path('.') else ""
                except ValueError:
                    continue
                
                # 计算文件夹大小和统计信息
                total_size = 0
                file_count = len(files)
                subfolder_count = len(dirs)
                
                try:
                    for file in files:
                        file_path = root_path / file
                        if file_path.exists():
                            total_size += file_path.stat().st_size
                except (OSError, PermissionError):
                    pass
                
                # 创建文件夹信息
                folder_info = FolderInfo(
                    path=str(root_path),
                    name=root_path.name,
                    parent_path=str(root_path.parent),
                    is_dir=True,
                    size=total_size,
                    file_count=file_count,
                    subfolder_count=subfolder_count
                )
                
                structure[rel_path_str] = folder_info
        
        except Exception as e:
            print(f"扫描文件夹时出错: {e}")
            return {}
        
        self.folder_structure = structure
        return structure

    def scan_file_structure(self) -> Dict[str, ItemInfo]:
        """
        扫描文件结构

        Returns:
            Dict[str, ItemInfo]: 文件结构字典，key为相对路径
        """
        if not self.source_path:
            return {}

        file_structure = {}
        source_path_obj = Path(self.source_path)

        try:
            # 递归扫描所有文件
            for root, dirs, files in os.walk(self.source_path):
                root_path = Path(root)

                # 处理当前文件夹中的文件
                for file in files:
                    file_path = root_path / file
                    try:
                        if file_path.exists():
                            file_size = file_path.stat().st_size

                            # 计算文件的相对路径
                            file_rel_path = file_path.relative_to(source_path_obj)
                            file_rel_path_str = str(file_rel_path)

                            # 创建文件信息
                            file_info = ItemInfo(
                                path=str(file_path),
                                name=file_path.name,
                                parent_path=str(file_path.parent),
                                is_dir=False,
                                size=file_size,
                                extension=file_path.suffix.lower()
                            )

                            file_structure[file_rel_path_str] = file_info
                    except (OSError, PermissionError):
                        pass

        except Exception as e:
            print(f"扫描文件时出错: {e}")
            return {}

        self.file_structure = file_structure
        return file_structure

    def scan_all_items(self) -> tuple[Dict[str, FolderInfo], Dict[str, ItemInfo]]:
        """
        扫描所有项目（文件夹和文件）

        Returns:
            tuple: (文件夹结构字典, 文件结构字典)
        """
        folder_structure = self.scan_folder_structure()
        file_structure = self.scan_file_structure()
        return folder_structure, file_structure

    def analyze_matches(self) -> List[MatchResult]:
        """
        分析匹配结果（包括文件夹和文件）

        Returns:
            List[MatchResult]: 匹配结果列表
        """
        if not self.folder_structure:
            self.scan_folder_structure()
        if not self.file_structure:
            self.scan_file_structure()

        matches = []

        # 分析文件夹匹配
        matches.extend(self._analyze_folder_matches())

        # 分析文件匹配
        matches.extend(self._analyze_file_matches())

        # 按置信度排序
        matches.sort(key=lambda x: x.confidence, reverse=True)
        return matches

    def _analyze_folder_matches(self) -> List[MatchResult]:
        """分析文件夹匹配"""
        matches = []

        for rel_path, folder_info in self.folder_structure.items():
            if not rel_path:  # 跳过根目录
                continue

            # 检查每个规则
            folder_matches = self.rule_manager.match_folder_to_categories(folder_info.name)

            best_match = None
            best_confidence = 0.0

            for category, keyword in folder_matches:
                # 计算匹配置信度
                confidence = self._calculate_confidence(folder_info.name, keyword)

                # 只保留置信度最高的匹配
                if confidence > best_confidence:
                    best_confidence = confidence
                    # 转换为ItemInfo
                    item_info = ItemInfo(
                        path=folder_info.path,
                        name=folder_info.name,
                        parent_path=folder_info.parent_path,
                        is_dir=folder_info.is_dir,
                        size=folder_info.size,
                        file_count=folder_info.file_count,
                        subfolder_count=folder_info.subfolder_count
                    )
                    best_match = MatchResult(
                        item_info=item_info,
                        category=category,
                        matched_keyword=keyword,
                        confidence=confidence
                    )

            # 如果找到匹配，添加到结果中
            if best_match:
                matches.append(best_match)

        return matches

    def _analyze_file_matches(self) -> List[MatchResult]:
        """分析文件匹配"""
        matches = []

        for rel_path, file_info in self.file_structure.items():
            # 检查每个规则
            file_matches = self.rule_manager.match_folder_to_categories(file_info.name)

            best_match = None
            best_confidence = 0.0

            for category, keyword in file_matches:
                # 计算匹配置信度
                confidence = self._calculate_confidence(file_info.name, keyword)

                # 只保留置信度最高的匹配
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_match = MatchResult(
                        item_info=file_info,
                        category=category,
                        matched_keyword=keyword,
                        confidence=confidence
                    )

            # 如果找到匹配，添加到结果中
            if best_match:
                matches.append(best_match)

        return matches
    
    def generate_move_operations(self, target_base_path: str = None) -> List[MoveOperation]:
        """
        生成移动操作列表
        
        Args:
            target_base_path: 目标基础路径，如果为None则自动生成
            
        Returns:
            List[MoveOperation]: 移动操作列表
        """
        matches = self.analyze_matches()
        
        if not target_base_path:
            target_base_path = self._generate_target_base_path()
        
        operations = []
        
        for match in matches:
            target_path = self._generate_target_path(match, target_base_path)

            operation = MoveOperation(
                source_path=match.item_info.path,
                target_path=target_path,
                category=match.category,
                matched_keyword=match.matched_keyword,
                item_info=match.item_info
            )
            operations.append(operation)
        
        return operations
    
    def get_unmatched_folders(self) -> List[FolderInfo]:
        """
        获取未匹配的文件夹列表
        
        Returns:
            List[FolderInfo]: 未匹配的文件夹列表
        """
        if not self.folder_structure:
            self.scan_folder_structure()
        
        matches = self.analyze_matches()
        matched_paths = {match.folder_info.path for match in matches}
        
        unmatched = []
        for rel_path, folder_info in self.folder_structure.items():
            if rel_path and folder_info.path not in matched_paths:
                unmatched.append(folder_info)
        
        return unmatched
    
    def get_analysis_summary(self) -> Dict:
        """
        获取分析摘要
        
        Returns:
            Dict: 分析摘要信息
        """
        if not self.folder_structure:
            self.scan_folder_structure()
        
        matches = self.analyze_matches()
        unmatched = self.get_unmatched_folders()
        
        # 按分类统计
        category_stats = {}
        for match in matches:
            category = match.category
            if category not in category_stats:
                category_stats[category] = {
                    'count': 0,
                    'folders': [],
                    'total_size': 0
                }
            category_stats[category]['count'] += 1
            category_stats[category]['folders'].append(match.item_info.name)
            category_stats[category]['total_size'] += match.item_info.size
        
        return {
            'total_folders': len(self.folder_structure) - 1,  # 减去根目录
            'matched_folders': len(matches),
            'unmatched_folders': len(unmatched),
            'categories': len(category_stats),
            'category_stats': category_stats,
            'unmatched_list': [f.name for f in unmatched]
        }
    
    def _calculate_confidence(self, folder_name: str, keyword: str) -> float:
        """
        计算匹配置信度
        
        Args:
            folder_name: 文件夹名称
            keyword: 关键词
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        folder_lower = folder_name.lower()
        keyword_lower = keyword.lower()
        
        # 完全匹配
        if folder_lower == keyword_lower:
            return 1.0
        
        # 开头匹配
        if folder_lower.startswith(keyword_lower):
            return 0.9
        
        # 结尾匹配
        if folder_lower.endswith(keyword_lower):
            return 0.8
        
        # 包含匹配
        if keyword_lower in folder_lower:
            # 根据关键词在文件夹名中的比例计算置信度
            ratio = len(keyword_lower) / len(folder_lower)
            return 0.5 + ratio * 0.3
        
        return 0.0
    
    def _generate_target_base_path(self) -> str:
        """
        生成目标基础路径
        
        Returns:
            str: 目标基础路径
        """
        if not self.source_path:
            return ""
        
        source_path_obj = Path(self.source_path)
        parent_dir = source_path_obj.parent
        source_name = source_path_obj.name
        
        target_name = f"{source_name}-整理版本"
        return str(parent_dir / target_name)
    
    def _generate_target_path(self, match: MatchResult, target_base_path: str) -> str:
        """
        生成目标路径（支持文件和文件夹）

        Args:
            match: 匹配结果
            target_base_path: 目标基础路径

        Returns:
            str: 目标路径
        """
        # 获取源项目的完整相对路径
        source_path_obj = Path(match.item_info.path)
        source_base_obj = Path(self.source_path)

        try:
            rel_path = source_path_obj.relative_to(source_base_obj)
            parent_parts = rel_path.parts[:-1]  # 除了最后一级的所有父级
            item_name = rel_path.parts[-1]      # 项目本身的名称（文件或文件夹）
        except ValueError:
            parent_parts = []
            item_name = source_path_obj.name

        # 构建目标路径: 目标基础路径/分类名/父级结构/项目名
        target_path_obj = Path(target_base_path) / match.category

        # 添加父级结构
        for part in parent_parts:
            target_path_obj = target_path_obj / part

        # 添加项目本身（文件或文件夹）
        target_path_obj = target_path_obj / item_name

        return str(target_path_obj)
