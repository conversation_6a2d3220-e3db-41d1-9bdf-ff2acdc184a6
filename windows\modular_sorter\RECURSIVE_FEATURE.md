# 递归处理子文件夹功能（树形结构）

## 功能概述

在第一个Tab页（拖拽排序）中新增了一个"🔄 递归处理子文件夹"勾选框，默认不勾选。当勾选此选项时，系统会：

1. **递归读取**拖进来的文件夹的子文件夹（支持多层嵌套）
2. **树形显示**所有文件和文件夹的层次结构
3. **独立编号**每一层内的文件命名规则都是独立的，每一层都从1开始编号
4. **预览重命名**右侧显示重命名后的预览结果

## 功能特点

### 1. 递归处理选项
- **位置**: 第一个Tab页的左侧按钮区域
- **默认状态**: 未勾选（非递归模式）
- **功能**: 控制是否递归处理子文件夹内容

### 2. 展开控制选项
- **选项**: "📂 展开所有树形结构"
- **默认状态**: 勾选（展开所有）
- **功能**: 控制树形结构的展开状态
  - 勾选：展开所有层级
  - 不勾选：默认展开2层

### 3. 顶级文件夹编号选项
- **选项**: "🏠 顶级文件夹参与编号"
- **默认状态**: 勾选（参与编号）
- **功能**: 控制是否显示和编号顶级文件夹
  - 勾选：显示顶级文件夹并参与编号
  - 不勾选：不显示顶级文件夹，直接显示内容

### 4. 两种处理模式

#### 非递归模式（默认）
- 只添加拖拽的文件夹本身到列表
- 不处理文件夹内部的文件和子文件夹
- 适用于只需要重命名文件夹本身的场景

#### 递归模式（勾选后）
- 递归遍历文件夹的所有子目录
- **树形显示**所有文件和子文件夹的层次结构
- 支持多层嵌套的文件夹结构
- **每层独立编号**：每个文件夹内的项目都从1开始编号
- **可控制顶级文件夹**：选择是否显示和编号顶级文件夹
- **灵活展开控制**：可选择展开所有或默认展开2层
- 适用于需要批量处理整个目录树的场景

### 5. 处理逻辑
- **文件**: 在其所在层级中按顺序编号
- **文件夹**: 在其所在层级中按顺序编号，内部文件重新从1开始
- **排序**: 每一层内按照文件夹在前、文件在后的规则排序
- **命名**: 每一层独立添加数字前缀（1. 2. 3. ...）
- **显示**: 左侧显示原始名称（带图标），右侧显示重命名预览
- **展开**: 默认展开2层，可选择展开所有层级
- **顶级文件夹**: 可选择是否显示和参与编号

### 6. 错误处理
- **权限错误**: 当无法访问某个文件夹时，显示权限错误提示
- **其他错误**: 捕获并显示处理过程中的其他错误

## 使用方法

### 1. 通过按钮添加文件夹
1. 勾选"🔄 递归处理子文件夹"选项（如需要）
2. 设置"📂 展开所有树形结构"选项（控制展开状态）
3. 设置"🏠 顶级文件夹参与编号"选项（控制是否显示顶级文件夹）
4. 点击"添加文件夹"按钮
5. 选择要处理的文件夹
6. 系统会根据设置处理文件夹内容

### 2. 通过拖拽添加文件夹
1. 勾选"🔄 递归处理子文件夹"选项（如需要）
2. 设置展开和顶级文件夹选项
3. 直接将文件夹拖拽到程序窗口
4. 系统会自动根据设置处理文件夹内容

## 示例

假设有以下文件夹结构：
```
西史-副本/
├── Coursebook某某一年级/
│   ├── 1A.pdf
│   └── 1B.pdf
├── Coursebook某某二年级/
│   └── 2A.pdf
├── 学生版教材三年级/
│   ├── 3A.pdf
│   └── 3B.pdf
├── 1A.pdf
├── 1B.pdf
├── 2A.pdf
├── 2B.pdf
├── 3A.pdf
└── 3B.pdf
```

### 非递归模式结果
```
原始名称                    新名称
📁 西史-副本               1. 西史-副本
```

### 递归模式结果（包含顶级文件夹）
```
原始名称                              新名称
▼ 📁 西史-副本                       1. 西史-副本
  ▼ 📁 Coursebook某某一年级           1. Coursebook某某一年级
    📄 1A.pdf                       1. 1A.pdf
    📄 1B.pdf                       2. 1B.pdf
  ▼ 📁 Coursebook某某二年级           2. Coursebook某某二年级
    📄 2A.pdf                       1. 2A.pdf
  ▼ 📁 学生版教材三年级               3. 学生版教材三年级
    📄 3A.pdf                       1. 3A.pdf
    📄 3B.pdf                       2. 3B.pdf
  📄 1A.pdf                         1. 1A.pdf
  📄 1B.pdf                         2. 1B.pdf
  📄 2A.pdf                         3. 2A.pdf
  📄 2B.pdf                         4. 2B.pdf
```

### 递归模式结果（不包含顶级文件夹）
```
原始名称                              新名称
▼ 📁 Coursebook某某一年级             1. Coursebook某某一年级
  📄 1A.pdf                         1. 1A.pdf
  📄 1B.pdf                         2. 1B.pdf
▼ 📁 Coursebook某某二年级             2. Coursebook某某二年级
  📄 2A.pdf                         1. 2A.pdf
▼ 📁 学生版教材三年级                 3. 学生版教材三年级
  📄 3A.pdf                         1. 3A.pdf
  📄 3B.pdf                         2. 3B.pdf
📄 1A.pdf                           1. 1A.pdf
📄 1B.pdf                           2. 1B.pdf
📄 2A.pdf                           3. 2A.pdf
📄 2B.pdf                           4. 2B.pdf
```

**注意**：
- `▼` 表示展开的文件夹，`▶` 表示折叠的文件夹
- 每个文件夹内的文件都从1开始编号
- 根目录的文件也从1开始编号
- 可以通过选项控制是否显示顶级文件夹

## 注意事项

1. **性能**: 递归处理大型目录树可能需要较长时间
2. **权限**: 确保对目标文件夹及其子文件夹有足够的访问权限
3. **存储**: 递归模式会将所有文件和文件夹加载到内存中
4. **排序**: 递归添加的项目会按照文件夹优先、文件在后的规则自动排序

## 技术实现

- 使用 `Path.iterdir()` 遍历目录内容
- 递归调用 `add_folder_contents_recursive()` 处理子文件夹
- 异常处理确保程序稳定性
- 状态反馈让用户了解处理进度
