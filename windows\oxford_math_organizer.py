#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
牛津新世代数学教材文件夹整理工具
专门处理按年级分类的教材文件夹整理
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Tuple
import re
import tkinter as tk
from tkinter import ttk, messagebox, filedialog


class OxfordMathOrganizer:
    """牛津数学教材文件夹整理器"""
    
    def __init__(self, root=None):
        self.root = root
        self.source_path = ""
        self.target_path = ""
        
        # 年级映射配置
        self.grade_mapping = {
            '4': '1. 中四',
            '5': '2. 中五',
            '6': '3. 中六'  # 可扩展
        }
        
        # 年级前缀模式
        self.grade_patterns = {
            '4': r'^4[A-Z]',  # 匹配4A, 4B等
            '5': r'^5[A-Z]',  # 匹配5A, 5B等
            '6': r'^6[A-Z]'   # 匹配6A, 6B等
        }
        
        # 存储扫描结果
        self.scan_results = []
        
        if root:
            self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("牛津数学教材整理工具")
        self.root.geometry("800x600")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 路径选择区域
        path_frame = ttk.LabelFrame(main_frame, text="路径设置", padding="10")
        path_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 源路径
        ttk.Label(path_frame, text="源路径:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.source_var = tk.StringVar()
        source_entry = ttk.Entry(path_frame, textvariable=self.source_var, width=60)
        source_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        ttk.Button(path_frame, text="浏览", command=self.browse_source).grid(row=0, column=2, pady=(0, 5))
        
        # 目标路径
        ttk.Label(path_frame, text="目标路径:").grid(row=1, column=0, sticky=tk.W)
        self.target_var = tk.StringVar()
        target_entry = ttk.Entry(path_frame, textvariable=self.target_var, width=60)
        target_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        ttk.Button(path_frame, text="浏览", command=self.browse_target).grid(row=1, column=2)
        
        path_frame.columnconfigure(1, weight=1)
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        ttk.Button(button_frame, text="扫描文件夹", command=self.scan_folders).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="预览整理", command=self.preview_organization).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="执行整理", command=self.execute_organization).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="创建目标结构", command=self.create_target_structure).pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="扫描结果", padding="10")
        result_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建Treeview显示扫描结果
        columns = ('source', 'target', 'grade', 'category')
        self.tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)
        
        self.tree.heading('source', text='源文件夹')
        self.tree.heading('target', text='目标位置')
        self.tree.heading('grade', text='年级')
        self.tree.heading('category', text='分类')
        
        self.tree.column('source', width=200)
        self.tree.column('target', width=200)
        self.tree.column('grade', width=80)
        self.tree.column('category', width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 状态栏
        self.status_var = tk.StringVar(value="准备就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 配置主框架的权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def browse_source(self):
        """浏览源文件夹"""
        folder = filedialog.askdirectory(title="选择源文件夹")
        if folder:
            self.source_var.set(folder)
            self.source_path = folder
    
    def browse_target(self):
        """浏览目标文件夹"""
        folder = filedialog.askdirectory(title="选择目标文件夹")
        if folder:
            self.target_var.set(folder)
            self.target_path = folder
    
    def scan_folders(self):
        """扫描源文件夹，找出需要整理的文件夹"""
        if not self.source_path:
            messagebox.showwarning("警告", "请先选择源文件夹")
            return
        
        if not os.path.exists(self.source_path):
            messagebox.showerror("错误", "源文件夹不存在")
            return
        
        self.scan_results.clear()
        self.tree.delete(*self.tree.get_children())
        
        try:
            source_dir = Path(self.source_path)
            self.status_var.set("正在扫描文件夹...")
            
            # 遍历源目录下的所有文件夹（A, B, C等）
            for category_folder in source_dir.iterdir():
                if not category_folder.is_dir():
                    continue
                
                category_name = category_folder.name
                self.status_var.set(f"正在扫描 {category_name}...")
                
                # 遍历分类文件夹下的子文件夹
                for sub_folder in category_folder.iterdir():
                    if not sub_folder.is_dir():
                        continue
                    
                    folder_name = sub_folder.name
                    grade = self.detect_grade(folder_name)
                    
                    if grade:
                        # 构建目标路径
                        target_grade_folder = self.grade_mapping[grade]
                        target_path = Path(self.target_path) / target_grade_folder / category_name / folder_name
                        
                        result = {
                            'source_path': str(sub_folder),
                            'target_path': str(target_path),
                            'grade': grade,
                            'category': category_name,
                            'folder_name': folder_name
                        }
                        
                        self.scan_results.append(result)
                        
                        # 添加到树形视图
                        self.tree.insert('', 'end', values=(
                            str(sub_folder),
                            str(target_path),
                            f"中{grade}",
                            category_name
                        ))
            
            self.status_var.set(f"扫描完成，找到 {len(self.scan_results)} 个需要整理的文件夹")
            
        except Exception as e:
            messagebox.showerror("错误", f"扫描文件夹时出错: {str(e)}")
            self.status_var.set("扫描失败")
    
    def detect_grade(self, folder_name: str) -> str:
        """检测文件夹名称对应的年级"""
        for grade, pattern in self.grade_patterns.items():
            if re.match(pattern, folder_name):
                return grade
        return ""
    
    def create_target_structure(self):
        """创建目标文件夹结构"""
        if not self.target_path:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return
        
        if not self.source_path:
            messagebox.showwarning("警告", "请先选择源文件夹以获取分类信息")
            return
        
        try:
            target_dir = Path(self.target_path)
            source_dir = Path(self.source_path)
            
            # 获取所有分类文件夹名称（A, B, C等）
            categories = [f.name for f in source_dir.iterdir() if f.is_dir()]
            
            created_folders = []
            
            # 为每个年级创建分类文件夹
            for grade, grade_folder in self.grade_mapping.items():
                grade_path = target_dir / grade_folder
                grade_path.mkdir(exist_ok=True)
                
                for category in categories:
                    category_path = grade_path / category
                    if not category_path.exists():
                        category_path.mkdir(parents=True, exist_ok=True)
                        created_folders.append(str(category_path))
            
            if created_folders:
                messagebox.showinfo("成功", f"成功创建了 {len(created_folders)} 个文件夹")
                self.status_var.set(f"创建了 {len(created_folders)} 个目标文件夹")
            else:
                messagebox.showinfo("信息", "目标文件夹结构已存在")
                self.status_var.set("目标文件夹结构已存在")
                
        except Exception as e:
            messagebox.showerror("错误", f"创建目标结构时出错: {str(e)}")
            self.status_var.set("创建目标结构失败")
    
    def preview_organization(self):
        """预览整理操作"""
        if not self.scan_results:
            messagebox.showwarning("警告", "请先扫描文件夹")
            return
        
        preview_text = "整理预览:\n\n"
        for result in self.scan_results:
            preview_text += f"源: {result['source_path']}\n"
            preview_text += f"目标: {result['target_path']}\n"
            preview_text += f"年级: 中{result['grade']} | 分类: {result['category']}\n"
            preview_text += "-" * 50 + "\n"
        
        # 创建预览窗口
        preview_window = tk.Toplevel(self.root)
        preview_window.title("整理预览")
        preview_window.geometry("600x400")
        
        text_widget = tk.Text(preview_window, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(preview_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.insert(tk.END, preview_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def execute_organization(self):
        """执行文件夹整理操作"""
        if not self.scan_results:
            messagebox.showwarning("警告", "请先扫描文件夹")
            return

        if not self.target_path:
            messagebox.showwarning("警告", "请先选择目标文件夹")
            return

        # 确认操作
        if not messagebox.askyesno("确认",
                                 f"确定要移动 {len(self.scan_results)} 个文件夹吗？\n"
                                 f"这个操作将把文件夹从源位置移动到目标位置。"):
            return

        success_count = 0
        error_count = 0
        error_messages = []

        try:
            for i, result in enumerate(self.scan_results):
                self.status_var.set(f"正在处理 {i+1}/{len(self.scan_results)}: {result['folder_name']}")
                self.root.update()

                source_path = Path(result['source_path'])
                target_path = Path(result['target_path'])

                try:
                    # 确保目标目录存在
                    target_path.parent.mkdir(parents=True, exist_ok=True)

                    # 检查源文件夹是否存在
                    if not source_path.exists():
                        error_messages.append(f"源文件夹不存在: {source_path}")
                        error_count += 1
                        continue

                    # 检查目标是否已存在
                    if target_path.exists():
                        if not messagebox.askyesno("文件夹已存在",
                                                 f"目标位置已存在文件夹:\n{target_path}\n"
                                                 f"是否覆盖？"):
                            continue
                        # 删除已存在的目标文件夹
                        shutil.rmtree(target_path)

                    # 移动文件夹
                    shutil.move(str(source_path), str(target_path))
                    success_count += 1

                except Exception as e:
                    error_messages.append(f"移动失败 {source_path} -> {target_path}: {str(e)}")
                    error_count += 1

            # 显示结果
            if error_count == 0:
                messagebox.showinfo("成功", f"成功移动了 {success_count} 个文件夹！")
                self.status_var.set(f"整理完成！成功移动 {success_count} 个文件夹")
                # 清空扫描结果
                self.scan_results.clear()
                self.tree.delete(*self.tree.get_children())
            else:
                error_text = "\n".join(error_messages[:10])  # 只显示前10个错误
                if len(error_messages) > 10:
                    error_text += f"\n... 还有 {len(error_messages) - 10} 个错误"

                messagebox.showwarning("部分成功",
                                     f"成功移动: {success_count} 个文件夹\n"
                                     f"失败: {error_count} 个文件夹\n\n"
                                     f"错误详情:\n{error_text}")
                self.status_var.set(f"整理完成！成功: {success_count}, 失败: {error_count}")

        except Exception as e:
            messagebox.showerror("错误", f"执行整理时出现严重错误: {str(e)}")
            self.status_var.set("整理失败")

    def organize_folders_programmatically(self, source_dir: str, target_dir: str) -> Dict:
        """
        程序化方式整理文件夹（无GUI）

        Args:
            source_dir: 源目录路径
            target_dir: 目标目录路径

        Returns:
            包含操作结果的字典
        """
        self.source_path = source_dir
        self.target_path = target_dir

        # 扫描文件夹
        self.scan_results.clear()

        try:
            source_path = Path(source_dir)
            target_path = Path(target_dir)

            if not source_path.exists():
                return {"success": False, "error": "源目录不存在"}

            # 扫描
            for category_folder in source_path.iterdir():
                if not category_folder.is_dir():
                    continue

                category_name = category_folder.name

                for sub_folder in category_folder.iterdir():
                    if not sub_folder.is_dir():
                        continue

                    folder_name = sub_folder.name
                    grade = self.detect_grade(folder_name)

                    if grade:
                        target_grade_folder = self.grade_mapping[grade]
                        target_folder_path = target_path / target_grade_folder / category_name / folder_name

                        result = {
                            'source_path': str(sub_folder),
                            'target_path': str(target_folder_path),
                            'grade': grade,
                            'category': category_name,
                            'folder_name': folder_name
                        }

                        self.scan_results.append(result)

            # 创建目标结构
            categories = [f.name for f in source_path.iterdir() if f.is_dir()]
            for grade, grade_folder in self.grade_mapping.items():
                grade_path = target_path / grade_folder
                grade_path.mkdir(exist_ok=True)

                for category in categories:
                    category_path = grade_path / category
                    category_path.mkdir(parents=True, exist_ok=True)

            # 执行移动
            success_count = 0
            error_count = 0
            error_messages = []

            for result in self.scan_results:
                try:
                    source_folder = Path(result['source_path'])
                    target_folder = Path(result['target_path'])

                    if not source_folder.exists():
                        error_messages.append(f"源文件夹不存在: {source_folder}")
                        error_count += 1
                        continue

                    # 确保目标目录存在
                    target_folder.parent.mkdir(parents=True, exist_ok=True)

                    # 如果目标已存在，删除它
                    if target_folder.exists():
                        shutil.rmtree(target_folder)

                    # 移动文件夹
                    shutil.move(str(source_folder), str(target_folder))
                    success_count += 1

                except Exception as e:
                    error_messages.append(f"移动失败 {result['source_path']}: {str(e)}")
                    error_count += 1

            return {
                "success": True,
                "total_found": len(self.scan_results),
                "success_count": success_count,
                "error_count": error_count,
                "error_messages": error_messages
            }

        except Exception as e:
            return {"success": False, "error": str(e)}


def main():
    """主函数"""
    root = tk.Tk()
    app = OxfordMathOrganizer(root)
    root.mainloop()


if __name__ == "__main__":
    main()
