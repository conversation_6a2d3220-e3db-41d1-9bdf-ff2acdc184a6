#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
牛津数学教材整理脚本
专门用于整理您的牛津新世代数学第三版教材
"""

from oxford_math_organizer import OxfordMathOrganizer
from pathlib import Path
import tkinter as tk


def quick_organize():
    """快速整理模式 - 使用预设路径"""
    # 您的实际路径
    source_path = r"D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）"
    target_path = r"D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）整理版本"
    
    print("牛津数学教材快速整理")
    print("=" * 50)
    print(f"源路径: {source_path}")
    print(f"目标路径: {target_path}")
    
    # 检查路径是否存在
    if not Path(source_path).exists():
        print("❌ 源路径不存在，请检查路径是否正确")
        return
    
    if not Path(target_path).exists():
        print("❌ 目标路径不存在，请检查路径是否正确")
        return
    
    # 创建整理器
    organizer = OxfordMathOrganizer()
    
    print("\n正在扫描和整理文件夹...")
    result = organizer.organize_folders_programmatically(source_path, target_path)
    
    # 显示结果
    if result["success"]:
        print("\n✅ 整理完成!")
        print(f"   找到需要整理的文件夹: {result['total_found']} 个")
        print(f"   成功移动: {result['success_count']} 个")
        print(f"   失败: {result['error_count']} 个")
        
        if result["error_messages"]:
            print("\n⚠️  错误信息:")
            for error in result["error_messages"][:5]:  # 只显示前5个错误
                print(f"   {error}")
            if len(result["error_messages"]) > 5:
                print(f"   ... 还有 {len(result['error_messages']) - 5} 个错误")
        
        print(f"\n📁 整理后的结构:")
        print(f"   {target_path}")
        print(f"   ├── 1. 中四/")
        print(f"   │   ├── A/ (包含4A, 4B文件夹)")
        print(f"   │   ├── B/ (包含4A, 4B文件夹)")
        print(f"   │   └── C/ (包含4A, 4B文件夹)")
        print(f"   └── 2. 中五/")
        print(f"       ├── A/ (包含5A, 5B文件夹)")
        print(f"       ├── B/ (包含5A, 5B文件夹)")
        print(f"       └── C/ (包含5A, 5B文件夹)")
        
    else:
        print(f"❌ 整理失败: {result['error']}")


def gui_organize():
    """图形界面整理模式"""
    print("启动图形界面...")
    root = tk.Tk()
    app = OxfordMathOrganizer(root)
    
    # 预设路径
    app.source_var.set(r"D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）")
    app.target_var.set(r"D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）整理版本")
    app.source_path = app.source_var.get()
    app.target_path = app.target_var.get()
    
    root.mainloop()


def preview_only():
    """仅预览模式 - 不实际移动文件"""
    source_path = r"D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）"
    target_path = r"D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）整理版本"
    
    print("牛津数学教材整理预览")
    print("=" * 50)
    
    if not Path(source_path).exists():
        print("❌ 源路径不存在，请检查路径是否正确")
        return
    
    # 创建整理器并扫描
    organizer = OxfordMathOrganizer()
    organizer.source_path = source_path
    organizer.target_path = target_path
    
    # 手动扫描（不移动文件）
    source_dir = Path(source_path)
    scan_results = []
    
    print("正在扫描文件夹...")
    
    for category_folder in source_dir.iterdir():
        if not category_folder.is_dir():
            continue
        
        category_name = category_folder.name
        print(f"扫描分类: {category_name}")
        
        for sub_folder in category_folder.iterdir():
            if not sub_folder.is_dir():
                continue
            
            folder_name = sub_folder.name
            grade = organizer.detect_grade(folder_name)
            
            if grade:
                target_grade_folder = organizer.grade_mapping[grade]
                target_folder_path = Path(target_path) / target_grade_folder / category_name / folder_name
                
                scan_results.append({
                    'source': str(sub_folder),
                    'target': str(target_folder_path),
                    'grade': grade,
                    'category': category_name,
                    'folder': folder_name
                })
    
    # 显示预览结果
    print(f"\n📋 找到 {len(scan_results)} 个需要整理的文件夹:")
    print("=" * 80)
    
    for result in scan_results:
        print(f"📁 {result['folder']} (中{result['grade']}, 分类{result['category']})")
        print(f"   从: {result['source']}")
        print(f"   到: {result['target']}")
        print()
    
    print("注意: 这只是预览，没有实际移动任何文件。")


def main():
    """主函数"""
    print("🎓 牛津新世代数学第三版教材整理工具")
    print("=" * 50)
    print("这个工具将帮助您整理牛津数学教材，按年级分类:")
    print("• 4A, 4B 文件夹 → 1. 中四")
    print("• 5A, 5B 文件夹 → 2. 中五")
    print("• 6A, 6B 文件夹 → 3. 中六")
    print()
    
    while True:
        print("请选择操作模式:")
        print("1. 🚀 快速整理 (使用预设路径，直接整理)")
        print("2. 🖥️  图形界面 (可以自定义路径，手动操作)")
        print("3. 👀 仅预览 (查看将要进行的操作，不实际移动文件)")
        print("4. 🚪 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            confirm = input("\n⚠️  确定要开始整理吗？这将移动文件夹！(y/N): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                quick_organize()
            else:
                print("已取消操作")
        elif choice == '2':
            gui_organize()
        elif choice == '3':
            preview_only()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        print("\n" + "-" * 50)


if __name__ == "__main__":
    main()
