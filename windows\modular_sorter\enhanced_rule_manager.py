"""
增强版规则管理器 - 支持启用/禁用和更多默认规则
"""
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional


class EnhancedRuleManager:
    """增强版分类规则管理器"""
    
    def __init__(self):
        """初始化规则管理器"""
        self.rules = {}  # {category_name: {"keywords": [...], "enabled": True/False}}
        self.rule_file = Path("enhanced_oxford_rules.json")
        self.load_rules()
    
    def get_default_rules(self) -> Dict:
        """获取默认规则集"""
        return {
            "中一": {"keywords": ["1A", "1B", "中一", "七年级"], "enabled": True},
            "中二": {"keywords": ["2A", "2B", "中二", "八年级"], "enabled": True},
            "中三": {"keywords": ["3A", "3B", "中三", "九年级"], "enabled": True},
            "中四": {"keywords": ["4A", "4B", "中四", "十年级"], "enabled": True},
            "中五": {"keywords": ["5A", "5B", "中五", "十一年级"], "enabled": True},
            "中六": {"keywords": ["6A", "6B", "中六", "十二年级"], "enabled": True},
            "小一": {"keywords": ["小一", "一年级", "P1"], "enabled": False},
            "小二": {"keywords": ["小二", "二年级", "P2"], "enabled": False},
            "小三": {"keywords": ["小三", "三年级", "P3"], "enabled": False},
            "小四": {"keywords": ["小四", "四年级", "P4"], "enabled": False},
            "小五": {"keywords": ["小五", "五年级", "P5"], "enabled": False},
            "小六": {"keywords": ["小六", "六年级", "P6"], "enabled": False},
            "初一": {"keywords": ["初一", "七年级", "Grade7"], "enabled": False},
            "初二": {"keywords": ["初二", "八年级", "Grade8"], "enabled": False},
            "初三": {"keywords": ["初三", "九年级", "Grade9"], "enabled": False},
            "高一": {"keywords": ["高一", "十年级", "Grade10"], "enabled": False},
            "高二": {"keywords": ["高二", "十一年级", "Grade11"], "enabled": False},
            "高三": {"keywords": ["高三", "十二年级", "Grade12"], "enabled": False},
            "数学": {"keywords": ["Math", "数学", "Mathematics"], "enabled": True},
            "英语": {"keywords": ["English", "英语", "英文"], "enabled": True},
            "科学": {"keywords": ["Science", "科学", "物理", "化学", "生物"], "enabled": False},
            "语文": {"keywords": ["Chinese", "语文", "中文"], "enabled": False},
            "历史": {"keywords": ["History", "历史"], "enabled": False},
            "地理": {"keywords": ["Geography", "地理"], "enabled": False},
            "练习册": {"keywords": ["练习", "习题", "Exercise", "Workbook"], "enabled": True},
            "教师用书": {"keywords": ["教师", "Teacher", "答案", "Answer"], "enabled": True},
            "测试卷": {"keywords": ["测试", "考试", "Test", "Exam"], "enabled": False},
            "课件": {"keywords": ["PPT", "课件", "Slides"], "enabled": False},
        }
    
    def add_rule(self, category: str, keywords: List[str], enabled: bool = True) -> bool:
        """
        添加分类规则
        
        Args:
            category: 分类名称
            keywords: 关键词列表
            enabled: 是否启用该规则
            
        Returns:
            bool: 是否添加成功
        """
        if not category or not keywords:
            return False
        
        # 清理关键词列表
        cleaned_keywords = [kw.strip() for kw in keywords if kw.strip()]
        if not cleaned_keywords:
            return False
        
        self.rules[category] = {
            "keywords": cleaned_keywords,
            "enabled": enabled
        }
        self.save_rules()
        return True
    
    def remove_rule(self, category: str) -> bool:
        """删除规则"""
        if category in self.rules:
            del self.rules[category]
            self.save_rules()
            return True
        return False
    
    def enable_rule(self, category: str, enabled: bool = True) -> bool:
        """启用/禁用规则"""
        if category in self.rules:
            self.rules[category]["enabled"] = enabled
            self.save_rules()
            return True
        return False
    
    def get_enabled_rules(self) -> Dict[str, List[str]]:
        """获取已启用的规则"""
        enabled_rules = {}
        for category, rule_data in self.rules.items():
            if rule_data.get("enabled", True):
                enabled_rules[category] = rule_data["keywords"]
        return enabled_rules
    
    def get_all_rules(self) -> Dict[str, Dict]:
        """获取所有规则（包含启用状态）"""
        return self.rules.copy()
    
    def match_folder_to_categories(self, folder_name: str) -> List[Tuple[str, str]]:
        """
        匹配文件夹名到分类
        
        Args:
            folder_name: 文件夹名称
            
        Returns:
            List[Tuple[str, str]]: [(分类名, 匹配的关键词), ...]
        """
        matches = []
        enabled_rules = self.get_enabled_rules()
        
        for category, keywords in enabled_rules.items():
            for keyword in keywords:
                if keyword.lower() in folder_name.lower():
                    matches.append((category, keyword))
        
        return matches
    
    def validate_rules(self) -> List[str]:
        """验证规则，返回错误信息列表"""
        errors = []
        enabled_rules = self.get_enabled_rules()
        
        # 检查关键词重复
        keyword_to_categories = {}
        for category, keywords in enabled_rules.items():
            for keyword in keywords:
                if keyword in keyword_to_categories:
                    keyword_to_categories[keyword].append(category)
                else:
                    keyword_to_categories[keyword] = [category]
        
        for keyword, categories in keyword_to_categories.items():
            if len(categories) > 1:
                errors.append(f"关键词 '{keyword}' 在多个分类中重复: {', '.join(categories)}")
        
        return errors
    
    def load_rules(self):
        """加载规则"""
        try:
            if self.rule_file.exists():
                with open(self.rule_file, 'r', encoding='utf-8') as f:
                    self.rules = json.load(f)
            else:
                # 首次运行，使用默认规则
                self.rules = self.get_default_rules()
                self.save_rules()
        except Exception as e:
            print(f"加载规则失败: {e}")
            # 使用默认规则
            self.rules = self.get_default_rules()
            self.save_rules()
    
    def save_rules(self):
        """保存规则"""
        try:
            with open(self.rule_file, 'w', encoding='utf-8') as f:
                json.dump(self.rules, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存规则失败: {e}")
    
    def import_rules(self, file_path: str) -> bool:
        """导入规则"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_rules = json.load(f)
            
            # 验证导入的规则格式
            for category, rule_data in imported_rules.items():
                if isinstance(rule_data, list):
                    # 兼容旧格式
                    imported_rules[category] = {
                        "keywords": rule_data,
                        "enabled": True
                    }
                elif not isinstance(rule_data, dict) or "keywords" not in rule_data:
                    return False
            
            self.rules.update(imported_rules)
            self.save_rules()
            return True
        except Exception as e:
            print(f"导入规则失败: {e}")
            return False
    
    def export_rules(self, file_path: str) -> bool:
        """导出规则"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.rules, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"导出规则失败: {e}")
            return False
    
    def reset_to_defaults(self):
        """重置为默认规则"""
        self.rules = self.get_default_rules()
        self.save_rules()
    
    def get_statistics(self) -> Dict:
        """获取规则统计信息"""
        total_rules = len(self.rules)
        enabled_rules = len([r for r in self.rules.values() if r.get("enabled", True)])
        total_keywords = sum(len(r["keywords"]) for r in self.rules.values())
        
        return {
            "total_rules": total_rules,
            "enabled_rules": enabled_rules,
            "disabled_rules": total_rules - enabled_rules,
            "total_keywords": total_keywords
        }
