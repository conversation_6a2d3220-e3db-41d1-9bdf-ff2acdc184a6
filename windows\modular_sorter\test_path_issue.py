"""
测试路径重复问题
"""
import os
import tempfile
from pathlib import Path
from enhanced_rule_manager import EnhancedRuleManager
from folder_analyzer import FolderAnalyzer


def create_simple_test_structure():
    """创建简单的测试文件夹结构"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="path_test_")
    base_path = Path(temp_dir) / "测试文件夹"
    base_path.mkdir(parents=True)
    
    # 创建测试文件夹结构
    test_folders = [
        "4A相关内容",
        "5B练习册",
        "A/4A内容",
        "B/4B内容", 
        "A/5A内容",
        "B/5B内容"
    ]
    
    for folder_path in test_folders:
        folder = base_path / folder_path
        folder.mkdir(parents=True, exist_ok=True)
        
        # 创建一些测试文件
        test_file = folder / "测试文件.txt"
        test_file.write_text("测试内容", encoding='utf-8')
    
    print(f"创建测试结构: {base_path}")
    return str(base_path)


def test_path_generation():
    """测试路径生成"""
    print("=== 测试路径生成 ===")
    
    # 创建测试结构
    source_path = create_simple_test_structure()
    target_base_path = str(Path(source_path).parent / "整理后")
    
    # 创建规则管理器，只启用必要的规则
    rm = EnhancedRuleManager()
    rm.enable_rule("中四", True)
    rm.enable_rule("中五", True)
    rm.enable_rule("练习册", True)
    
    # 创建文件夹分析器
    fa = FolderAnalyzer(rm)
    fa.set_source_path(source_path)
    
    # 扫描文件夹
    fa.scan_folder_structure()
    print(f"扫描到的文件夹结构:")
    for rel_path, folder_info in fa.folder_structure.items():
        if rel_path:  # 跳过根目录
            print(f"  {rel_path} -> {folder_info.path}")
    
    # 分析匹配
    matches = fa.analyze_matches()
    print(f"\n匹配结果:")
    for match in matches:
        print(f"  项目: {match.item_info.name}")
        print(f"  完整路径: {match.item_info.path}")
        print(f"  类型: {'文件夹' if match.item_info.is_dir else '文件'}")
        print(f"  匹配分类: {match.category}")
        print(f"  匹配关键词: {match.matched_keyword}")

        # 生成目标路径
        target_path = fa._generate_target_path(match, target_base_path)
        print(f"  目标路径: {target_path}")

        # 显示相对路径
        source_rel = str(Path(match.item_info.path).relative_to(source_path))
        target_rel = str(Path(target_path).relative_to(target_base_path))
        print(f"  路径映射: {source_rel} → {target_rel}")
        print()
    
    # 生成移动操作
    operations = fa.generate_move_operations(target_base_path)
    print(f"移动操作:")
    for i, op in enumerate(operations, 1):
        source_rel = str(Path(op.source_path).relative_to(source_path))
        target_rel = str(Path(op.target_path).relative_to(target_base_path))
        print(f"  {i:2d}. {source_rel} → {target_rel}")
    
    print(f"\n测试文件夹位置: {source_path}")


if __name__ == "__main__":
    test_path_generation()
