"""
操作执行器 - 执行文件移动、复制等操作
"""
import shutil
import os
from pathlib import Path
from typing import List, Dict, Callable, Optional
from dataclasses import dataclass
from enum import Enum

from folder_analyzer import MoveOperation


class OperationType(Enum):
    """操作类型"""
    MOVE = "move"
    COPY = "copy"


class ConflictStrategy(Enum):
    """冲突处理策略"""
    SKIP = "skip"          # 跳过
    OVERWRITE = "overwrite"  # 覆盖
    RENAME = "rename"      # 重命名


@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    operation: MoveOperation
    error_message: str = ""
    actual_target_path: str = ""


@dataclass
class ExecutionSummary:
    """执行摘要"""
    total_operations: int
    successful_operations: int
    failed_operations: int
    skipped_operations: int
    results: List[ExecutionResult]
    errors: List[str]


class OperationExecutor:
    """操作执行器"""
    
    def __init__(self, progress_callback: Optional[Callable] = None):
        """
        初始化操作执行器
        
        Args:
            progress_callback: 进度回调函数，接收 (current, total, message) 参数
        """
        self.progress_callback = progress_callback
        self.conflict_strategy = ConflictStrategy.RENAME
        self.operation_type = OperationType.MOVE
        self.delete_empty_folders = True
        
    def set_conflict_strategy(self, strategy: ConflictStrategy):
        """设置冲突处理策略"""
        self.conflict_strategy = strategy
    
    def set_operation_type(self, op_type: OperationType):
        """设置操作类型"""
        self.operation_type = op_type
    
    def set_delete_empty_folders(self, delete: bool):
        """设置是否删除空文件夹"""
        self.delete_empty_folders = delete
    
    def validate_operations(self, operations: List[MoveOperation]) -> List[str]:
        """
        验证操作的有效性
        
        Args:
            operations: 操作列表
            
        Returns:
            List[str]: 错误信息列表，空列表表示无错误
        """
        errors = []
        
        if not operations:
            errors.append("没有要执行的操作")
            return errors
        
        # 检查源路径是否存在
        for i, op in enumerate(operations):
            source_path = Path(op.source_path)
            if not source_path.exists():
                errors.append(f"操作 {i+1}: 源路径不存在 - {op.source_path}")
            elif not source_path.is_dir():
                errors.append(f"操作 {i+1}: 源路径不是文件夹 - {op.source_path}")
        
        # 检查目标路径的父目录是否可创建
        target_parents = set()
        for op in operations:
            target_path = Path(op.target_path)
            target_parents.add(target_path.parent)
        
        for parent in target_parents:
            try:
                parent.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建目标目录 {parent}: {str(e)}")
        
        # 检查磁盘空间（如果是复制操作）
        if self.operation_type == OperationType.COPY:
            total_size = 0
            for op in operations:
                try:
                    total_size += self._get_folder_size(op.source_path)
                except Exception:
                    pass
            
            if total_size > 0:
                target_drive = Path(operations[0].target_path).drive
                if target_drive:
                    try:
                        free_space = shutil.disk_usage(target_drive).free
                        if total_size > free_space:
                            errors.append(f"磁盘空间不足，需要 {total_size // (1024**3):.1f}GB，可用 {free_space // (1024**3):.1f}GB")
                    except Exception:
                        pass
        
        return errors
    
    def execute_operations(self, operations: List[MoveOperation]) -> ExecutionSummary:
        """
        执行操作列表
        
        Args:
            operations: 操作列表
            
        Returns:
            ExecutionSummary: 执行摘要
        """
        results = []
        errors = []
        successful_count = 0
        failed_count = 0
        skipped_count = 0
        
        total_operations = len(operations)
        
        for i, operation in enumerate(operations):
            if self.progress_callback:
                self.progress_callback(i, total_operations, f"正在处理: {operation.folder_info.name}")
            
            try:
                result = self._execute_single_operation(operation)
                results.append(result)
                
                if result.success:
                    successful_count += 1
                else:
                    failed_count += 1
                    if result.error_message:
                        errors.append(f"{operation.folder_info.name}: {result.error_message}")
                        
            except Exception as e:
                failed_count += 1
                error_msg = f"执行操作时出错: {str(e)}"
                errors.append(f"{operation.folder_info.name}: {error_msg}")
                
                result = ExecutionResult(
                    success=False,
                    operation=operation,
                    error_message=error_msg
                )
                results.append(result)
        
        # 清理空文件夹
        if self.delete_empty_folders and self.operation_type == OperationType.MOVE:
            self._cleanup_empty_folders(operations)
        
        if self.progress_callback:
            self.progress_callback(total_operations, total_operations, "操作完成")
        
        return ExecutionSummary(
            total_operations=total_operations,
            successful_operations=successful_count,
            failed_operations=failed_count,
            skipped_operations=skipped_count,
            results=results,
            errors=errors
        )
    
    def _execute_single_operation(self, operation: MoveOperation) -> ExecutionResult:
        """
        执行单个操作
        
        Args:
            operation: 移动操作
            
        Returns:
            ExecutionResult: 执行结果
        """
        source_path = Path(operation.source_path)
        target_path = Path(operation.target_path)
        
        # 检查源路径
        if not source_path.exists():
            return ExecutionResult(
                success=False,
                operation=operation,
                error_message="源路径不存在"
            )
        
        # 创建目标目录
        try:
            target_path.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            return ExecutionResult(
                success=False,
                operation=operation,
                error_message=f"创建目标目录失败: {str(e)}"
            )
        
        # 确定最终目标路径
        final_target = target_path / source_path.name
        final_target = self._resolve_conflict(final_target)
        
        # 执行操作
        try:
            if self.operation_type == OperationType.MOVE:
                shutil.move(str(source_path), str(final_target))
            else:  # COPY
                if source_path.is_dir():
                    shutil.copytree(str(source_path), str(final_target))
                else:
                    shutil.copy2(str(source_path), str(final_target))
            
            return ExecutionResult(
                success=True,
                operation=operation,
                actual_target_path=str(final_target)
            )
            
        except Exception as e:
            return ExecutionResult(
                success=False,
                operation=operation,
                error_message=f"执行{self.operation_type.value}操作失败: {str(e)}"
            )
    
    def _resolve_conflict(self, target_path: Path) -> Path:
        """
        解决路径冲突
        
        Args:
            target_path: 目标路径
            
        Returns:
            Path: 解决冲突后的路径
        """
        if not target_path.exists():
            return target_path
        
        if self.conflict_strategy == ConflictStrategy.OVERWRITE:
            return target_path
        elif self.conflict_strategy == ConflictStrategy.SKIP:
            return target_path  # 调用方需要检查是否跳过
        else:  # RENAME
            counter = 1
            original_target = target_path
            
            while target_path.exists():
                if original_target.is_dir():
                    target_path = original_target.parent / f"{original_target.name}_{counter}"
                else:
                    target_path = original_target.parent / f"{original_target.stem}_{counter}{original_target.suffix}"
                counter += 1
            
            return target_path
    
    def _get_folder_size(self, folder_path: str) -> int:
        """
        获取文件夹大小
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            int: 文件夹大小（字节）
        """
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, FileNotFoundError):
                        pass
        except Exception:
            pass
        return total_size
    
    def _cleanup_empty_folders(self, operations: List[MoveOperation]):
        """
        清理空文件夹
        
        Args:
            operations: 操作列表
        """
        if self.operation_type != OperationType.MOVE:
            return
        
        # 收集所有源文件夹的父目录
        parent_dirs = set()
        for op in operations:
            source_path = Path(op.source_path)
            parent_dirs.add(source_path.parent)
        
        # 从最深层开始清理空文件夹
        for parent_dir in sorted(parent_dirs, key=lambda x: len(x.parts), reverse=True):
            try:
                if parent_dir.exists() and parent_dir.is_dir():
                    # 检查是否为空
                    if not any(parent_dir.iterdir()):
                        parent_dir.rmdir()
            except Exception:
                pass  # 忽略清理错误
