"""
模块化文件排序工具启动脚本
运行这个文件来启动新的模块化版本
"""
import sys
import os
from pathlib import Path

# 添加modular_sorter目录到Python路径
current_dir = Path(__file__).parent
modular_dir = current_dir / "modular_sorter"
sys.path.insert(0, str(modular_dir))

# 导入并运行主程序
from main import main

if __name__ == "__main__":
    print("启动模块化文件排序工具...")
    print("功能特点：")
    print("- ✅ 窗口默认置顶功能")
    print("- ✅ 修复了待移动文件列表中路径显示过长的问题")
    print("- ✅ 修复了移动文件后需要重新选中的问题")
    print("- ✅ 右侧拖拽文件自动按名称排序（去除数字前缀）")
    print("- ✅ 修复了文件夹排序问题（去除数字前缀）")
    print("- ✅ 修复了移动文件后右侧列表不刷新的问题")
    print("- ✅ 新增批量创建文件夹功能")
    print("- ✅ 新增自动合并移动功能")
    print("- ✅ 新增空文件夹清理功能")
    print("- ✅ 拖入新数据时自动清空之前的数据")
    print("- ✅ 支持F5键刷新当前标签页内容")
    print("- ✅ 使用模块化架构，代码更清晰易维护")
    print("- ✅ 保持了原有的所有功能")
    print()
    print("使用提示：")
    print("- 拖入新文件/文件夹会自动清空之前的数据")
    print("- 按F5键可以刷新当前标签页的内容")
    print("- 自动合并移动：拖入多个项目，自动生成合并文件夹名称并移动")
    print("- 空文件夹清理：扫描并清理文件夹中的所有空文件夹")
    print()
    main()
