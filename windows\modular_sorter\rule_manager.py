"""
规则管理器 - 处理分类规则的增删改查和持久化
"""
import json
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional


class RuleManager:
    """分类规则管理器"""
    
    def __init__(self):
        """初始化规则管理器"""
        self.rules = {}  # {category_name: {"keywords": [...], "enabled": True/False}}
        self.rule_file = Path("oxford_rules.json")
        self.load_rules()
    
    def add_rule(self, category: str, keywords: List[str]) -> bool:
        """
        添加分类规则
        
        Args:
            category: 分类名称（如"四年级"）
            keywords: 关键词列表（如["4A", "4B"]）
            
        Returns:
            bool: 是否添加成功
        """
        if not category or not keywords:
            return False
            
        # 清理关键词列表
        cleaned_keywords = [kw.strip() for kw in keywords if kw.strip()]
        if not cleaned_keywords:
            return False
            
        self.rules[category] = cleaned_keywords
        self.save_rules()
        return True
    
    def remove_rule(self, category: str) -> bool:
        """
        删除分类规则
        
        Args:
            category: 分类名称
            
        Returns:
            bool: 是否删除成功
        """
        if category in self.rules:
            del self.rules[category]
            self.save_rules()
            return True
        return False
    
    def update_rule(self, category: str, keywords: List[str]) -> bool:
        """
        更新分类规则
        
        Args:
            category: 分类名称
            keywords: 新的关键词列表
            
        Returns:
            bool: 是否更新成功
        """
        return self.add_rule(category, keywords)
    
    def get_rule(self, category: str) -> Optional[List[str]]:
        """
        获取指定分类的规则
        
        Args:
            category: 分类名称
            
        Returns:
            Optional[List[str]]: 关键词列表，如果不存在返回None
        """
        return self.rules.get(category)
    
    def get_all_rules(self) -> Dict[str, List[str]]:
        """
        获取所有规则
        
        Returns:
            Dict[str, List[str]]: 所有规则字典
        """
        return self.rules.copy()
    
    def clear_rules(self):
        """清空所有规则"""
        self.rules.clear()
        self.save_rules()
    
    def match_folder_to_categories(self, folder_name: str) -> List[Tuple[str, str]]:
        """
        根据规则匹配文件夹到分类
        
        Args:
            folder_name: 文件夹名称
            
        Returns:
            List[Tuple[str, str]]: 匹配结果列表，每个元素为(分类名, 匹配的关键词)
        """
        matches = []
        folder_lower = folder_name.lower()
        
        for category, keywords in self.rules.items():
            for keyword in keywords:
                if keyword.lower() in folder_lower:
                    matches.append((category, keyword))
        
        return matches
    
    def validate_rules(self) -> List[str]:
        """
        验证规则的有效性
        
        Returns:
            List[str]: 错误信息列表，空列表表示无错误
        """
        errors = []
        
        if not self.rules:
            errors.append("没有配置任何规则")
            return errors
        
        # 检查重复的关键词
        all_keywords = []
        for category, keywords in self.rules.items():
            if not keywords:
                errors.append(f"分类 '{category}' 没有配置关键词")
                continue
                
            for keyword in keywords:
                if keyword in all_keywords:
                    errors.append(f"关键词 '{keyword}' 在多个分类中重复")
                else:
                    all_keywords.append(keyword)
        
        return errors
    
    def save_rules(self) -> bool:
        """
        保存规则到文件
        
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(self.rule_file, 'w', encoding='utf-8') as f:
                json.dump(self.rules, f, ensure_ascii=False, indent=2)
            return True
        except Exception:
            return False
    
    def load_rules(self) -> bool:
        """
        从文件加载规则
        
        Returns:
            bool: 是否加载成功
        """
        try:
            if self.rule_file.exists():
                with open(self.rule_file, 'r', encoding='utf-8') as f:
                    self.rules = json.load(f)
                return True
            else:
                # 创建默认规则
                self.create_default_rules()
                return True
        except Exception:
            self.create_default_rules()
            return False
    
    def create_default_rules(self):
        """创建默认规则"""
        self.rules = {
            "四年级": ["4A", "4B"],
            "五年级": ["5A", "5B"],
            "六年级": ["6A", "6B"]
        }
        self.save_rules()
    
    def export_rules(self, file_path: str) -> bool:
        """
        导出规则到指定文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.rules, f, ensure_ascii=False, indent=2)
            return True
        except Exception:
            return False
    
    def import_rules(self, file_path: str) -> bool:
        """
        从指定文件导入规则
        
        Args:
            file_path: 导入文件路径
            
        Returns:
            bool: 是否导入成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_rules = json.load(f)
            
            # 验证导入的规则格式
            if isinstance(imported_rules, dict):
                for category, keywords in imported_rules.items():
                    if not isinstance(keywords, list):
                        return False
                
                self.rules = imported_rules
                self.save_rules()
                return True
            return False
        except Exception:
            return False
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取规则统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        total_categories = len(self.rules)
        total_keywords = sum(len(keywords) for keywords in self.rules.values())
        
        return {
            "total_categories": total_categories,
            "total_keywords": total_keywords,
            "avg_keywords_per_category": total_keywords / total_categories if total_categories > 0 else 0
        }
