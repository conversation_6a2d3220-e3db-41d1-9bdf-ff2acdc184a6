#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
牛津数学教材整理工具测试脚本
演示如何使用 OxfordMathOrganizer 类
"""

import os
from pathlib import Path
from oxford_math_organizer import OxfordMathOrganizer


def create_test_structure():
    """创建测试用的文件夹结构"""
    # 测试源目录
    test_source = Path("test_oxford_source")
    test_source.mkdir(exist_ok=True)
    
    # 创建分类文件夹 A, B, C
    categories = ['A', 'B', 'C']
    grade_folders = ['4A', '4B', '5A', '5B', '6A', '6B']
    
    for category in categories:
        category_path = test_source / category
        category_path.mkdir(exist_ok=True)
        
        # 在每个分类下创建年级文件夹
        for grade_folder in grade_folders:
            grade_path = category_path / grade_folder
            grade_path.mkdir(exist_ok=True)
            
            # 创建一些示例文件
            (grade_path / f"example_{grade_folder}.txt").write_text(f"这是 {category}/{grade_folder} 的示例文件")
    
    # 创建目标目录
    test_target = Path("test_oxford_target")
    test_target.mkdir(exist_ok=True)
    
    print(f"测试结构已创建:")
    print(f"源目录: {test_source.absolute()}")
    print(f"目标目录: {test_target.absolute()}")
    
    return str(test_source.absolute()), str(test_target.absolute())


def test_programmatic_usage():
    """测试程序化使用方式"""
    print("=" * 50)
    print("测试程序化使用方式")
    print("=" * 50)
    
    # 创建测试结构
    source_dir, target_dir = create_test_structure()
    
    # 创建整理器实例（不需要GUI）
    organizer = OxfordMathOrganizer()
    
    # 执行整理
    result = organizer.organize_folders_programmatically(source_dir, target_dir)
    
    # 显示结果
    if result["success"]:
        print(f"✅ 整理成功!")
        print(f"   找到文件夹: {result['total_found']} 个")
        print(f"   成功移动: {result['success_count']} 个")
        print(f"   失败: {result['error_count']} 个")
        
        if result["error_messages"]:
            print("\n错误信息:")
            for error in result["error_messages"]:
                print(f"   ❌ {error}")
    else:
        print(f"❌ 整理失败: {result['error']}")
    
    # 验证结果
    print("\n验证整理结果:")
    target_path = Path(target_dir)
    
    for grade_folder in ["1. 中四", "2. 中五", "3. 中六"]:
        grade_path = target_path / grade_folder
        if grade_path.exists():
            print(f"\n📁 {grade_folder}:")
            for category in ['A', 'B', 'C']:
                category_path = grade_path / category
                if category_path.exists():
                    folders = [f.name for f in category_path.iterdir() if f.is_dir()]
                    if folders:
                        print(f"   📂 {category}: {', '.join(sorted(folders))}")


def test_gui_usage():
    """测试GUI使用方式"""
    print("\n" + "=" * 50)
    print("启动GUI测试")
    print("=" * 50)
    print("GUI窗口将会打开，您可以:")
    print("1. 选择源文件夹和目标文件夹")
    print("2. 点击'扫描文件夹'查看需要整理的文件夹")
    print("3. 点击'创建目标结构'创建年级分类文件夹")
    print("4. 点击'预览整理'查看整理计划")
    print("5. 点击'执行整理'进行实际的文件夹移动")
    
    import tkinter as tk
    root = tk.Tk()
    app = OxfordMathOrganizer(root)
    root.mainloop()


def cleanup_test_files():
    """清理测试文件"""
    import shutil
    
    test_dirs = ["test_oxford_source", "test_oxford_target"]
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            shutil.rmtree(test_dir)
            print(f"已清理测试目录: {test_dir}")


def main():
    """主函数"""
    print("牛津数学教材整理工具测试")
    print("=" * 50)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 程序化测试（自动创建测试文件夹并整理）")
        print("2. GUI测试（打开图形界面）")
        print("3. 清理测试文件")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            test_programmatic_usage()
        elif choice == '2':
            test_gui_usage()
        elif choice == '3':
            cleanup_test_files()
        elif choice == '4':
            print("退出测试")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
