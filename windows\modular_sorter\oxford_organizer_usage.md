# 牛津教材分类整理器 - 使用说明

## 功能概述

牛津教材分类整理器是一个智能文件分类整理工具，能够根据用户定义的规则，自动识别和整理教材文件夹结构。

## 主要特性

- ✅ **智能识别**: 根据文件夹名称中的关键词自动分类
- ✅ **灵活规则**: 支持自定义分类规则，可导入导出
- ✅ **预览分析**: 详细显示整理结果预览，确保操作准确
- ✅ **安全操作**: 支持移动/复制模式，提供操作确认
- ✅ **进度跟踪**: 实时显示操作进度和结果统计
- ✅ **错误处理**: 完善的错误处理和冲突解决机制

## 使用步骤

### 1. 启动程序
```bash
cd windows/modular_sorter
python main.py
```

### 2. 选择"牛津教材整理"标签页

### 3. 选择源文件夹
- **方法一**: 直接拖拽文件夹到蓝色区域
- **方法二**: 点击"浏览"按钮选择文件夹

### 4. 配置分类规则
- 在"分类名称"输入框中输入分类名称（如"四年级"）
- 在"关键词"输入框中输入关键词，多个关键词用逗号分隔（如"4A,4B"）
- 点击"添加规则"按钮
- 可以添加多个分类规则

#### 默认规则示例：
- 四年级: 4A, 4B
- 五年级: 5A, 5B  
- 六年级: 6A, 6B

### 5. 分析文件夹
- 点击"分析文件夹"按钮
- 系统会扫描源文件夹结构并根据规则进行匹配
- 在预览树中查看分析结果

### 6. 预览操作详情
- 点击"预览详情"按钮查看详细的操作计划
- 确认移动操作的源路径和目标路径
- 检查未匹配的文件夹列表

### 7. 执行整理操作
- 选择操作类型：移动文件 或 复制文件
- 选择是否删除空文件夹
- 点击"开始整理"按钮
- 确认操作后开始执行

## 典型使用场景

### 场景：整理牛津数学教材

**源文件夹结构**:
```
牛津新世代数学第三版/
├── A/
│   ├── 4A相关内容/
│   ├── 5A相关内容/
│   └── 其他内容/
├── B/
│   ├── 4B相关内容/
│   ├── 5B相关内容/
│   └── 其他内容/
└── C/
    ├── 包含4A的文件夹/
    ├── 包含5B的文件夹/
    └── 其他内容/
```

**配置规则**:
- 四年级: 4A, 4B
- 五年级: 5A, 5B

**整理后结构**:
```
牛津新世代数学第三版-整理版本/
├── 四年级/
│   ├── A/
│   │   └── 4A相关内容/
│   ├── B/
│   │   └── 4B相关内容/
│   └── C/
│       └── 包含4A的文件夹/
└── 五年级/
    ├── A/
    │   └── 5A相关内容/
    ├── B/
    │   └── 5B相关内容/
    └── C/
        └── 包含5B的文件夹/
```

## 高级功能

### 规则管理
- **导入规则**: 从JSON文件导入预设规则
- **导出规则**: 将当前规则保存为JSON文件
- **删除规则**: 选中规则后点击删除

### 操作选项
- **移动模式**: 将文件夹移动到新位置（原位置文件夹消失）
- **复制模式**: 复制文件夹到新位置（原位置文件夹保留）
- **删除空文件夹**: 移动后自动清理空的源文件夹

### 冲突处理
- 如果目标位置已存在同名文件夹，系统会自动重命名（添加序号）
- 例如：`4A相关内容` → `4A相关内容_1`

## 注意事项

### 使用前准备
1. 确保有足够的磁盘空间（复制模式需要双倍空间）
2. 关闭可能占用文件的程序（如资源管理器、编辑器等）
3. 建议先使用复制模式测试，确认结果后再使用移动模式

### 安全提醒
- **移动操作不可撤销**，请务必先预览确认
- 建议对重要文件先进行备份
- 如有疑问，优先使用复制模式

### 性能优化
- 大量文件处理时可能需要较长时间，请耐心等待
- 避免在操作过程中关闭程序或断电
- 网络驱动器操作速度较慢，建议先复制到本地

## 故障排除

### 常见问题

**Q: 拖拽文件夹没有反应？**
A: 确保拖拽到蓝色区域，并且文件夹路径不包含特殊字符

**Q: 分析结果显示"无匹配"？**
A: 检查规则配置是否正确，关键词是否与文件夹名称匹配

**Q: 执行操作时提示权限错误？**
A: 以管理员身份运行程序，或检查文件夹权限设置

**Q: 操作中断或失败？**
A: 查看错误信息，检查磁盘空间、文件占用等问题

### 技术支持
如遇到其他问题，请查看程序状态栏的错误信息，或联系技术支持。

## 更新日志

### v1.0.0 (2024-08-03)
- 首次发布
- 支持基本的文件夹分类整理功能
- 提供规则管理和预览功能
- 集成到模块化文件排序工具中
