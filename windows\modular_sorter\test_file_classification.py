"""
测试文件分类功能
"""
import os
import tempfile
from pathlib import Path
from enhanced_rule_manager import EnhancedRuleManager
from folder_analyzer import FolderAnalyzer


def create_mixed_test_structure():
    """创建包含文件和文件夹的测试结构"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="file_classification_test_")
    base_path = Path(temp_dir) / "测试文件夹"
    base_path.mkdir(parents=True)
    
    # 创建测试文件夹结构
    test_folders = [
        "A/4A内容",
        "A/5A内容", 
        "B/4B内容",
        "B/5B内容",
        "练习册文件夹"
    ]
    
    for folder_path in test_folders:
        folder = base_path / folder_path
        folder.mkdir(parents=True, exist_ok=True)
        
        # 在文件夹中创建一些测试文件
        test_file = folder / "测试文件.txt"
        test_file.write_text("测试内容", encoding='utf-8')
    
    # 创建测试文件（直接在各级目录中）
    test_files = [
        "4A数学练习.pdf",
        "5B英语作业.docx", 
        "A/4A测试卷.pdf",
        "A/5A练习册.pdf",
        "B/4B教师用书.pdf",
        "B/5B课件.pptx",
        "练习册-4A版本.pdf",
        "教师用书-5B数学.pdf",
        "中四综合测试.docx",
        "中五期末考试.pdf"
    ]
    
    for file_path in test_files:
        file = base_path / file_path
        file.parent.mkdir(parents=True, exist_ok=True)
        file.write_text(f"这是{file.name}的内容", encoding='utf-8')
    
    print(f"创建混合测试结构: {base_path}")
    return str(base_path)


def test_file_and_folder_classification():
    """测试文件和文件夹分类"""
    print("=== 测试文件和文件夹分类 ===")
    
    # 创建测试结构
    source_path = create_mixed_test_structure()
    target_base_path = str(Path(source_path).parent / "整理后")
    
    # 创建规则管理器
    rm = EnhancedRuleManager()
    rm.enable_rule("中四", True)
    rm.enable_rule("中五", True)
    rm.enable_rule("数学", True)
    rm.enable_rule("英语", True)
    rm.enable_rule("练习册", True)
    rm.enable_rule("教师用书", True)
    
    # 创建分析器
    fa = FolderAnalyzer(rm)
    fa.set_source_path(source_path)
    
    # 扫描结构
    folder_structure, file_structure = fa.scan_all_items()
    
    print(f"扫描结果:")
    print(f"  文件夹数量: {len(folder_structure)}")
    print(f"  文件数量: {len(file_structure)}")
    
    print(f"\n文件夹结构:")
    for rel_path, folder_info in folder_structure.items():
        if rel_path:  # 跳过根目录
            print(f"  📁 {rel_path}")
    
    print(f"\n文件结构:")
    for rel_path, file_info in file_structure.items():
        print(f"  📄 {rel_path} ({file_info.extension})")
    
    # 分析匹配
    matches = fa.analyze_matches()
    print(f"\n匹配结果 (共{len(matches)}个):")
    
    folder_matches = [m for m in matches if m.item_info.is_dir]
    file_matches = [m for m in matches if not m.item_info.is_dir]
    
    print(f"  文件夹匹配: {len(folder_matches)}个")
    print(f"  文件匹配: {len(file_matches)}个")
    
    print(f"\n文件夹匹配详情:")
    for match in folder_matches:
        source_rel = str(Path(match.item_info.path).relative_to(source_path))
        target_path = fa._generate_target_path(match, target_base_path)
        target_rel = str(Path(target_path).relative_to(target_base_path))
        print(f"  📁 {source_rel} → {target_rel} (匹配: {match.matched_keyword})")
    
    print(f"\n文件匹配详情:")
    for match in file_matches:
        source_rel = str(Path(match.item_info.path).relative_to(source_path))
        target_path = fa._generate_target_path(match, target_base_path)
        target_rel = str(Path(target_path).relative_to(target_base_path))
        print(f"  📄 {source_rel} → {target_rel} (匹配: {match.matched_keyword})")
    
    # 生成移动操作
    operations = fa.generate_move_operations(target_base_path)
    print(f"\n移动操作 (共{len(operations)}个):")
    
    folder_ops = [op for op in operations if op.item_info.is_dir]
    file_ops = [op for op in operations if not op.item_info.is_dir]
    
    print(f"  文件夹操作: {len(folder_ops)}个")
    print(f"  文件操作: {len(file_ops)}个")
    
    print(f"\n所有操作详情:")
    for i, op in enumerate(operations, 1):
        source_rel = str(Path(op.source_path).relative_to(source_path))
        target_rel = str(Path(op.target_path).relative_to(target_base_path))
        item_type = "📁" if op.item_info.is_dir else "📄"
        print(f"  {i:2d}. {item_type} {source_rel} → {target_rel}")
    
    # 验证路径结构
    print(f"\n路径结构验证:")
    expected_patterns = [
        ("4A", "中四"),
        ("5A", "中五"), 
        ("4B", "中四"),
        ("5B", "中五"),
        ("练习", "练习册"),
        ("教师", "教师用书"),
        ("数学", "数学"),
        ("英语", "英语")
    ]
    
    for pattern, expected_category in expected_patterns:
        matching_ops = [op for op in operations if pattern in op.item_info.name]
        if matching_ops:
            for op in matching_ops:
                if op.category == expected_category:
                    print(f"  ✅ {op.item_info.name} → {op.category} (正确)")
                else:
                    print(f"  ❌ {op.item_info.name} → {op.category} (期望: {expected_category})")
    
    print(f"\n测试文件夹位置: {source_path}")


if __name__ == "__main__":
    test_file_and_folder_classification()
