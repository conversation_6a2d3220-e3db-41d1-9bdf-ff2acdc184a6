#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空文件夹清理管理器
支持扫描文件夹中的所有空文件夹，以树形结构显示并支持批量删除
"""

import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import os
from utils import get_display_name


class EmptyFolderCleaner:
    """空文件夹清理管理器"""
    
    def __init__(self, parent_frame, status_callback=None):
        """初始化空文件夹清理管理器"""
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        self.root_folders = []  # 存储根文件夹路径
        self.empty_folders = []  # 存储所有空文件夹信息
        self.folder_tree_data = {}  # 存储文件夹树形数据
        
    def setup_empty_folder_tab(self, notebook):
        """设置空文件夹清理标签页"""
        # 创建标签页
        self.tab_frame = ttk.Frame(notebook)
        notebook.add(self.tab_frame, text="空文件夹清理")
        
        # 创建主要布局
        main_frame = ttk.Frame(self.tab_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 说明标签
        info_frame = ttk.LabelFrame(main_frame, text="功能说明", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_text = """拖入文件夹，扫描并显示其中所有的空文件夹：
• 以树形结构显示所有文件夹
• 空文件夹会被特别标记（红色显示）
• 支持选择性删除空文件夹
• 递归扫描所有子文件夹"""
        
        info_label = tk.Label(info_frame, text=info_text, justify=tk.LEFT, 
                             font=("Arial", 9), wraplength=600)
        info_label.pack(anchor=tk.W)
        
        # 文件夹列表区域（左侧）
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        folder_frame = ttk.LabelFrame(left_frame, text="待扫描文件夹", padding=10)
        folder_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置grid权重
        folder_frame.grid_rowconfigure(0, weight=1)
        folder_frame.grid_columnconfigure(0, weight=1)
        
        # 创建文件夹列表
        self.folder_listbox = tk.Listbox(folder_frame, selectmode=tk.EXTENDED)
        self.folder_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件夹列表滚动条
        folder_scrollbar = ttk.Scrollbar(folder_frame, orient=tk.VERTICAL, command=self.folder_listbox.yview)
        self.folder_listbox.configure(yscrollcommand=folder_scrollbar.set)
        folder_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 设置拖拽支持
        self.folder_listbox.drop_target_register('DND_Files')
        self.folder_listbox.dnd_bind('<<Drop>>', self.on_drop)
        
        # 设置键盘事件
        self.folder_listbox.bind('<F5>', self.on_f5_refresh)
        self.folder_listbox.focus_set()
        
        # 左侧按钮
        left_button_frame = ttk.Frame(left_frame)
        left_button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(left_button_frame, text="清空列表", command=self.clear_folder_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_button_frame, text="移除选中", command=self.remove_selected_folders).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_button_frame, text="扫描空文件夹", command=self.scan_empty_folders, 
                  style="Accent.TButton").pack(side=tk.RIGHT)
        
        # 树形显示区域（右侧）
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        tree_frame = ttk.LabelFrame(right_frame, text="文件夹结构（红色=空文件夹）", padding=10)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置grid权重
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 创建Treeview来显示文件夹结构
        self.tree = ttk.Treeview(tree_frame, show='tree', selectmode='extended')
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 树形滚动条
        tree_scrollbar_v = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        tree_scrollbar_h = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=tree_scrollbar_v.set, xscrollcommand=tree_scrollbar_h.set)
        tree_scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        tree_scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置树形样式
        self.tree.tag_configure('empty', foreground='red', font=('Arial', 9, 'bold'))
        self.tree.tag_configure('normal', foreground='black', font=('Arial', 9))
        
        # 右侧按钮
        right_button_frame = ttk.Frame(right_frame)
        right_button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(right_button_frame, text="全选空文件夹", command=self.select_all_empty).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_button_frame, text="取消选择", command=self.clear_selection).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_button_frame, text="删除选中", command=self.delete_selected_folders, 
                  style="Accent.TButton").pack(side=tk.RIGHT)
        
    def on_drop(self, event):
        """处理拖拽事件 - 清空之前的数据，加载新数据"""
        try:
            # 清空之前的数据
            self.clear_folder_list()
            self.clear_tree()
            
            # 获取根窗口来访问tk.splitlist
            root = self.parent_frame.winfo_toplevel()
            files = root.tk.splitlist(event.data)
            
            added_count = 0
            for file_path in files:
                path_obj = Path(file_path)
                if path_obj.exists() and path_obj.is_dir():
                    self.root_folders.append(file_path)
                    display_name = get_display_name(file_path, max_length=40)
                    self.folder_listbox.insert(tk.END, display_name)
                    added_count += 1
            
            self._update_status(f"已清空并重新加载 {added_count} 个文件夹")
            
        except Exception as e:
            self._update_status(f"拖拽失败: {str(e)}", "lightcoral")
            
    def clear_folder_list(self):
        """清空文件夹列表"""
        self.root_folders.clear()
        self.folder_listbox.delete(0, tk.END)
        self._update_status("已清空文件夹列表", "lightblue")
        
    def clear_tree(self):
        """清空树形显示"""
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.empty_folders.clear()
        self.folder_tree_data.clear()
        
    def remove_selected_folders(self):
        """移除选中的文件夹"""
        selection = self.folder_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要移除的文件夹")
            return
            
        # 从后往前删除，避免索引变化
        for i in reversed(selection):
            self.folder_listbox.delete(i)
            if 0 <= i < len(self.root_folders):
                self.root_folders.pop(i)
                
        self._update_status(f"已移除 {len(selection)} 个文件夹")
        # 清空树形显示
        self.clear_tree()
        
    def is_folder_empty(self, folder_path):
        """检查文件夹是否为空（递归检查）"""
        try:
            path_obj = Path(folder_path)
            if not path_obj.exists() or not path_obj.is_dir():
                return False
                
            # 检查是否有任何文件
            for item in path_obj.rglob('*'):
                if item.is_file():
                    return False
                    
            return True
        except (PermissionError, OSError):
            return False
            
    def scan_folder_structure(self, root_path, parent_item=""):
        """递归扫描文件夹结构"""
        try:
            root_path_obj = Path(root_path)
            if not root_path_obj.exists() or not root_path_obj.is_dir():
                return
                
            # 获取所有子文件夹
            subfolders = []
            try:
                for item in root_path_obj.iterdir():
                    if item.is_dir():
                        subfolders.append(item)
            except (PermissionError, OSError) as e:
                self._update_status(f"无法访问文件夹: {root_path} - {str(e)}", "orange")
                return
                
            # 按名称排序
            subfolders.sort(key=lambda x: x.name.lower())
            
            for subfolder in subfolders:
                # 检查是否为空文件夹
                is_empty = self.is_folder_empty(str(subfolder))
                
                # 添加到树形控件
                tag = 'empty' if is_empty else 'normal'
                folder_name = subfolder.name
                if is_empty:
                    folder_name += " [空]"
                    
                item_id = self.tree.insert(parent_item, 'end', text=folder_name, tags=(tag,))
                
                # 存储文件夹信息
                self.folder_tree_data[item_id] = {
                    'path': str(subfolder),
                    'is_empty': is_empty,
                    'name': subfolder.name
                }
                
                if is_empty:
                    self.empty_folders.append({
                        'path': str(subfolder),
                        'name': subfolder.name,
                        'item_id': item_id
                    })
                
                # 递归扫描子文件夹
                self.scan_folder_structure(str(subfolder), item_id)
                
        except Exception as e:
            self._update_status(f"扫描文件夹结构失败: {str(e)}", "lightcoral")
            
    def scan_empty_folders(self):
        """扫描空文件夹"""
        if not self.root_folders:
            messagebox.showwarning("警告", "请先拖入要扫描的文件夹")
            return
            
        # 清空之前的结果
        self.clear_tree()
        
        self._update_status("正在扫描空文件夹...", "yellow")
        
        try:
            total_empty = 0
            for root_folder in self.root_folders:
                # 添加根文件夹到树形控件
                root_path_obj = Path(root_folder)
                root_is_empty = self.is_folder_empty(root_folder)
                
                tag = 'empty' if root_is_empty else 'normal'
                folder_name = root_path_obj.name
                if root_is_empty:
                    folder_name += " [空]"
                    
                root_item = self.tree.insert('', 'end', text=folder_name, tags=(tag,))
                
                # 存储根文件夹信息
                self.folder_tree_data[root_item] = {
                    'path': root_folder,
                    'is_empty': root_is_empty,
                    'name': root_path_obj.name
                }
                
                if root_is_empty:
                    self.empty_folders.append({
                        'path': root_folder,
                        'name': root_path_obj.name,
                        'item_id': root_item
                    })
                    total_empty += 1
                
                # 递归扫描子文件夹
                self.scan_folder_structure(root_folder, root_item)
                
            # 展开所有节点
            self.expand_all_nodes()
            
            self._update_status(f"扫描完成，发现 {len(self.empty_folders)} 个空文件夹", "lightgreen")
            
        except Exception as e:
            self._update_status(f"扫描失败: {str(e)}", "lightcoral")
            
    def expand_all_nodes(self):
        """展开所有树形节点"""
        def expand_item(item):
            self.tree.item(item, open=True)
            for child in self.tree.get_children(item):
                expand_item(child)
                
        for item in self.tree.get_children():
            expand_item(item)
            
    def select_all_empty(self):
        """选择所有空文件夹"""
        self.tree.selection_remove(self.tree.selection())
        
        empty_items = []
        for folder_info in self.empty_folders:
            empty_items.append(folder_info['item_id'])
            
        if empty_items:
            self.tree.selection_set(empty_items)
            self._update_status(f"已选择 {len(empty_items)} 个空文件夹")
        else:
            self._update_status("没有找到空文件夹", "orange")
            
    def clear_selection(self):
        """清除选择"""
        self.tree.selection_remove(self.tree.selection())
        self._update_status("已清除选择", "lightblue")
        
    def delete_selected_folders(self):
        """删除选中的文件夹"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的文件夹")
            return
            
        # 获取选中的文件夹路径
        selected_folders = []
        for item_id in selection:
            if item_id in self.folder_tree_data:
                folder_info = self.folder_tree_data[item_id]
                if folder_info['is_empty']:
                    selected_folders.append(folder_info)
                    
        if not selected_folders:
            messagebox.showwarning("警告", "选中的项目中没有空文件夹")
            return
            
        # 确认删除
        folder_list = "\n".join([f"• {info['path']}" for info in selected_folders[:10]])
        if len(selected_folders) > 10:
            folder_list += f"\n... 还有 {len(selected_folders) - 10} 个文件夹"
            
        if not messagebox.askyesno("确认删除", 
                                 f"确定要删除以下 {len(selected_folders)} 个空文件夹吗？\n\n{folder_list}\n\n这个操作不可撤销！"):
            return
            
        # 执行删除
        success_count = 0
        error_count = 0
        error_messages = []
        
        # 按路径长度倒序排序，先删除深层文件夹
        selected_folders.sort(key=lambda x: len(x['path']), reverse=True)
        
        for folder_info in selected_folders:
            try:
                folder_path = Path(folder_info['path'])
                if folder_path.exists() and folder_path.is_dir():
                    # 再次确认是空文件夹
                    if self.is_folder_empty(str(folder_path)):
                        folder_path.rmdir()
                        success_count += 1
                    else:
                        error_count += 1
                        error_messages.append(f"{folder_info['name']}: 文件夹不为空")
                else:
                    error_count += 1
                    error_messages.append(f"{folder_info['name']}: 文件夹不存在")
                    
            except Exception as e:
                error_count += 1
                error_messages.append(f"{folder_info['name']}: {str(e)}")
                
        # 显示结果
        if error_count == 0:
            messagebox.showinfo("成功", f"成功删除 {success_count} 个空文件夹")
            self._update_status(f"成功删除 {success_count} 个空文件夹")
        else:
            error_text = "\n".join(error_messages[:3])  # 只显示前3个错误
            if len(error_messages) > 3:
                error_text += f"\n... 还有 {len(error_messages) - 3} 个错误"
            messagebox.showwarning("部分成功", 
                                 f"成功删除 {success_count} 个文件夹\n失败 {error_count} 个\n\n错误详情：\n{error_text}")
            self._update_status(f"删除完成，{error_count} 个失败", "orange")
            
        # 重新扫描
        if success_count > 0:
            self.scan_empty_folders()
            
    def on_f5_refresh(self, event=None):
        """F5刷新文件夹列表"""
        if not self.root_folders:
            self._update_status("列表为空，无需刷新", "lightblue")
            return
            
        # 检查文件夹是否仍然存在
        valid_folders = []
        removed_count = 0
        
        for i, folder_path in enumerate(self.root_folders):
            path_obj = Path(folder_path)
            if path_obj.exists() and path_obj.is_dir():
                valid_folders.append(folder_path)
            else:
                removed_count += 1
                
        self.root_folders = valid_folders
        
        # 重新填充列表框
        self.folder_listbox.delete(0, tk.END)
        for folder_path in self.root_folders:
            display_name = get_display_name(folder_path, max_length=40)
            self.folder_listbox.insert(tk.END, display_name)
            
        # 清空树形显示
        self.clear_tree()
        
        if removed_count > 0:
            self._update_status(f"已刷新列表，移除了 {removed_count} 个不存在的文件夹", "orange")
        else:
            self._update_status("已刷新列表，所有文件夹有效", "lightblue")
            
    def handle_system_drop(self, files):
        """处理系统拖拽的文件 - 清空之前的数据，加载新数据"""
        # 清空之前的数据
        self.clear_folder_list()
        self.clear_tree()
        
        added_count = 0
        for file_path in files:
            path_obj = Path(file_path)
            if path_obj.exists() and path_obj.is_dir():
                self.root_folders.append(file_path)
                display_name = get_display_name(file_path, max_length=40)
                self.folder_listbox.insert(tk.END, display_name)
                added_count += 1
        
        self._update_status(f"已清空并重新加载 {added_count} 个文件夹")
        
    def _update_status(self, message, color="lightgreen"):
        """更新状态栏"""
        if self.status_callback:
            self.status_callback(message, color)
