# 文件/文件夹排序工具 - 使用指南

## 🎯 如何查看树形结构

### 问题：为什么我看不到树形结构？

**答案**：您需要勾选"🔄 递归处理子文件夹"选项！

### 📋 操作步骤

1. **勾选递归选项**
   - 在左侧找到"🔄 递归处理子文件夹"选项
   - ✅ 勾选这个选项

2. **设置展开选项**（可选）
   - ✅ "📂 展开所有树形结构" - 展开所有层级
   - ❌ "📂 展开所有树形结构" - 只展开前2层（推荐）

3. **设置顶级文件夹选项**（可选）
   - ✅ "🏠 顶级文件夹参与编号" - 显示顶级文件夹
   - ❌ "🏠 顶级文件夹参与编号" - 不显示顶级文件夹

4. **拖拽文件夹**
   - 将包含子文件夹的文件夹拖拽到程序窗口
   - 或点击"添加文件夹"按钮选择文件夹

### 📊 两种模式对比

#### 非递归模式（默认）
```
原始名称                    新名称
📁 西史                    1. 西史
📁 西史 - 副本             2. 西史 - 副本
```
- 只显示拖拽的文件夹本身
- 不显示内部文件和子文件夹
- 适合只重命名文件夹本身

#### 递归模式（勾选后）
```
原始名称                              新名称
▼ 📁 根文件夹                        1. 根文件夹
  ▼ 📁 西史                          1. 西史
    ▶ 📁 子文件夹1                    1. 子文件夹1
      ▶ 📄 子文件1.txt               1. 子文件1.txt
    ▶ 📄 文件1.txt                   1. 文件1.txt
    ▶ 📄 文件2.txt                   2. 文件2.txt
  ▼ 📁 西史 - 副本                    2. 西史 - 副本
    ▶ 📄 副本文件1.txt               1. 副本文件1.txt
```
- 显示完整的树形结构
- 每层独立编号
- 可展开/折叠查看内容
- 适合批量处理整个目录树

## ✏️ 如何编辑新名称

### 双击编辑功能

1. **定位到新名称列**
   - 在右侧"新名称"列中找到要修改的项目

2. **双击编辑**
   - 双击右侧的新名称（如"1. 西史"）
   - 会出现编辑框

3. **完成编辑**
   - 输入新的名称
   - 按 `Enter` 确认
   - 按 `Esc` 取消
   - 点击其他地方也会确认

### 📝 编辑示例

**编辑前**：`1. 西史`
**编辑中**：`1. 中国历史`
**编辑后**：`1. 中国历史`

## 🔧 完整功能列表

### 基础功能
- ✅ 添加文件/文件夹
- ✅ 拖拽添加
- ✅ 自动排序和编号
- ✅ 批量重命名

### 高级功能
- ✅ 递归处理子文件夹
- ✅ 树形结构显示
- ✅ 展开/折叠控制
- ✅ 顶级文件夹控制
- ✅ 双击编辑新名称
- ✅ 去除数字前缀选项

### 控制选项说明

| 选项 | 功能 | 默认状态 |
|------|------|----------|
| 🔄 递归处理子文件夹 | 是否显示树形结构 | ❌ 未勾选 |
| 📂 展开所有树形结构 | 控制展开层级 | ✅ 勾选 |
| 🏠 顶级文件夹参与编号 | 是否显示顶级文件夹 | ✅ 勾选 |
| 排序时去除开头序号 | 排序时忽略数字前缀 | ✅ 勾选 |

## 🚀 快速开始

### 查看树形结构
1. ✅ 勾选"🔄 递归处理子文件夹"
2. 拖拽文件夹到程序窗口
3. 查看完整的树形结构

### 编辑名称
1. 双击右侧"新名称"列的任意项目
2. 输入新名称
3. 按Enter确认

### 保存重命名
1. 编辑完所有需要的名称
2. 点击"保存重命名"按钮
3. 确认操作

## ❓ 常见问题

**Q: 为什么看不到树形结构？**
A: 请确保勾选了"🔄 递归处理子文件夹"选项

**Q: 如何编辑新名称？**
A: 双击右侧"新名称"列的项目即可编辑

**Q: 如何控制展开层级？**
A: 使用"📂 展开所有树形结构"选项控制

**Q: 顶级文件夹要不要显示？**
A: 使用"🏠 顶级文件夹参与编号"选项控制
