"""
拖拽排序管理器
处理文件/文件夹的拖拽排序功能
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from utils import natural_sort_key, remove_number_prefix


class DragSortManager:
    """拖拽排序管理器"""
    
    def __init__(self, parent_frame, status_callback=None):
        """
        初始化拖拽排序管理器
        
        Args:
            parent_frame: 父框架
            status_callback: 状态更新回调函数
        """
        self.parent_frame = parent_frame
        self.status_callback = status_callback
        
        # 数据存储
        self.items = []
        
        # 拖拽相关变量
        self.drag_item = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.drag_active = False
        self.highlight_item = None
        self.drag_threshold = 5
        
        # UI组件
        self.tree = None
        self.remove_prefix_var = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.parent_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置主框架的行列权重
        main_frame.columnconfigure(1, weight=3)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="文件/文件夹排序工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.N), padx=(0, 10))
        
        # 添加文件按钮
        ttk.Button(button_frame, text="添加文件", command=self.add_files).pack(fill=tk.X, pady=2)
        
        # 添加文件夹按钮
        ttk.Button(button_frame, text="添加文件夹", command=self.add_folders).pack(fill=tk.X, pady=2)
        
        # 清空列表按钮
        ttk.Button(button_frame, text="清空列表", command=self.clear_list).pack(fill=tk.X, pady=2)
        
        # 排序选项
        self.remove_prefix_var = tk.BooleanVar(value=True)
        self.prefix_checkbox = ttk.Checkbutton(
            button_frame, 
            text="排序时去除开头序号", 
            variable=self.remove_prefix_var,
            command=self.on_prefix_option_changed
        )
        self.prefix_checkbox.pack(fill=tk.X, pady=2)
        
        # 分隔线
        ttk.Separator(button_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # 保存按钮
        ttk.Button(button_frame, text="保存重命名", command=self.save_changes,
                  style="Accent.TButton").pack(fill=tk.X, pady=2)
        
        # 列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview用于显示文件列表
        self.tree = ttk.Treeview(list_frame, columns=('new_name',), show='tree headings')
        self.tree.heading('#0', text='原始名称')
        self.tree.heading('new_name', text='新名称')

        # 优化列宽设置
        self.tree.column('#0', width=250, minwidth=200)
        self.tree.column('new_name', width=350, minwidth=300)
        
        # 配置拖拽样式
        self.tree.tag_configure('dragging', background='lightblue')
        self.tree.tag_configure('drop_target', background='lightgreen')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 设置拖拽事件
        self.tree.bind('<Button-1>', self.on_click)
        self.tree.bind('<B1-Motion>', self.on_drag)
        self.tree.bind('<ButtonRelease-1>', self.on_drop)
        self.tree.bind('<Motion>', self.on_motion)
        self.tree.bind('<Leave>', self.on_leave)

        # 设置键盘事件
        self.tree.bind('<F5>', self.on_f5_refresh)
        self.tree.focus_set()  # 确保可以接收键盘事件
    
    def add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(title="选择要排序的文件")
        if files:
            added_any = False
            for file_path in files:
                if self.add_item(file_path):
                    added_any = True
            if added_any:
                self.auto_reorder_items()
                self._update_status(f"已添加 {len(files)} 个文件")

    def add_folders(self):
        """添加文件夹"""
        folder = filedialog.askdirectory(title="选择要排序的文件夹")
        if folder:
            if self.add_item(folder):
                self.auto_reorder_items()
                self._update_status("已添加文件夹")

    def add_item(self, path):
        """添加单个项目"""
        path_obj = Path(path)
        if not path_obj.exists():
            return False

        # 检查是否已存在
        for item in self.items:
            if item['path'] == path:
                return False

        # 添加到列表
        self.items.append({
            'path': path,
            'original_name': path_obj.name,
            'display_name': path_obj.name,
            'is_folder': path_obj.is_dir()
        })
        return True

    def clear_list(self):
        """清空列表"""
        self.items.clear()
        for item in self.tree.get_children():
            self.tree.delete(item)
        self._update_status("已清空列表")

    def on_prefix_option_changed(self):
        """前缀选项改变时重新排序"""
        if self.items:
            self.auto_reorder_items()

    def sort_items(self):
        """按照文件夹在前、文件在后的规则排序，各自按名称排序"""
        folders = [item for item in self.items if item['is_folder']]
        files = [item for item in self.items if not item['is_folder']]

        if self.remove_prefix_var.get():
            folders.sort(key=lambda x: natural_sort_key(remove_number_prefix(x['original_name'])))
            files.sort(key=lambda x: natural_sort_key(remove_number_prefix(x['original_name'])))
        else:
            folders.sort(key=lambda x: natural_sort_key(x['original_name']))
            files.sort(key=lambda x: natural_sort_key(x['original_name']))

        self.items = folders + files

    def auto_reorder_items(self):
        """自动重新排序并添加数字前缀"""
        self.sort_items()

        for i, item in enumerate(self.items, 1):
            clean_name = remove_number_prefix(item['original_name'])
            new_name = f"{i}. {clean_name}"
            item['display_name'] = new_name

        self.refresh_tree()

    def update_display(self):
        """更新显示（不重新排序，保持当前顺序）"""
        for i, item in enumerate(self.items, 1):
            clean_name = remove_number_prefix(item['original_name'])
            new_name = f"{i}. {clean_name}"
            item['display_name'] = new_name

        self.refresh_tree()

    def refresh_tree(self):
        """刷新树形视图"""
        for item in self.tree.get_children():
            self.tree.delete(item)

        for item in self.items:
            icon = "📁" if item['is_folder'] else "📄"
            item_id = self.tree.insert('', 'end', text=f"{icon} {item['original_name']}",
                                     values=(item['display_name'],))
    
    def _update_status(self, message, color="lightgreen"):
        """更新状态信息"""
        if self.status_callback:
            self.status_callback(message, color)

    # 拖拽事件处理
    def on_click(self, event):
        """鼠标点击事件"""
        item = self.tree.identify_row(event.y)
        if item:
            self.drag_item = item
            self.drag_start_x = event.x
            self.drag_start_y = event.y
            self.drag_active = False

    def on_drag(self, event):
        """拖拽事件"""
        if not self.drag_item:
            return

        # 计算拖拽距离
        distance = ((event.x - self.drag_start_x) ** 2 + (event.y - self.drag_start_y) ** 2) ** 0.5

        if distance > self.drag_threshold and not self.drag_active:
            self.drag_active = True
            try:
                self.tree.item(self.drag_item, tags=('dragging',))
            except tk.TclError:
                pass
            self.tree.configure(cursor="fleur")

        if self.drag_active:
            target_item = self.tree.identify_row(event.y)

            # 清除之前的高亮
            if self.highlight_item and self.highlight_item != self.drag_item:
                try:
                    self.tree.set(self.highlight_item, 'new_name',
                                self.get_item_display_name(self.highlight_item))
                    self.tree.item(self.highlight_item, tags=())
                except tk.TclError:
                    pass

            # 高亮目标位置
            if target_item and target_item != self.drag_item:
                try:
                    self.highlight_item = target_item
                    self.tree.set(target_item, 'new_name', f"🎯 插入到这里")
                    self.tree.item(target_item, tags=('drop_target',))
                except tk.TclError:
                    self.highlight_item = None

    def on_drop(self, event):
        """放下事件"""
        if not self.drag_item or not self.drag_active:
            self.cleanup_drag()
            return

        target_item = self.tree.identify_row(event.y)
        if target_item and target_item != self.drag_item:
            # 获取拖拽项目的索引
            drag_index = None
            target_index = None

            for i, item_id in enumerate(self.tree.get_children()):
                if item_id == self.drag_item:
                    drag_index = i
                if item_id == target_item:
                    target_index = i

            if drag_index is not None and target_index is not None:
                # 重新排列items列表
                item = self.items.pop(drag_index)
                self.items.insert(target_index, item)

                # 手动更新显示（不调用auto_reorder_items，保持用户的拖拽顺序）
                self.update_display()

        self.cleanup_drag()

    def on_motion(self, event):
        """鼠标移动事件（非拖拽状态）"""
        if not self.drag_active:
            item = self.tree.identify_row(event.y)
            if item:
                self.tree.configure(cursor="hand2")
            else:
                self.tree.configure(cursor="")

    def on_leave(self, event):
        """鼠标离开事件"""
        if not self.drag_active:
            self.tree.configure(cursor="")

    def cleanup_drag(self):
        """清理拖拽状态"""
        # 清除高亮
        if self.highlight_item:
            try:
                self.tree.set(self.highlight_item, 'new_name',
                            self.get_item_display_name(self.highlight_item))
                self.tree.item(self.highlight_item, tags=())
            except tk.TclError:
                pass

        # 清除拖拽项目的样式
        if self.drag_item:
            try:
                self.tree.item(self.drag_item, tags=())
            except tk.TclError:
                pass

        self.drag_item = None
        self.drag_active = False
        self.highlight_item = None
        self.tree.configure(cursor="")

    def get_item_display_name(self, item_id):
        """获取项目的显示名称"""
        for i, tree_item in enumerate(self.tree.get_children()):
            if tree_item == item_id and i < len(self.items):
                return self.items[i]['display_name']
        return ""

    def save_changes(self):
        """保存重命名更改"""
        if not self.items:
            messagebox.showwarning("警告", "没有文件需要重命名")
            return

        success_count = 0
        error_count = 0

        for item in self.items:
            try:
                old_path = Path(item['path'])
                new_path = old_path.parent / item['display_name']

                if old_path != new_path:
                    old_path.rename(new_path)
                    success_count += 1

            except Exception as e:
                error_count += 1
                print(f"重命名失败: {item['original_name']} -> {item['display_name']}, 错误: {e}")

        if error_count == 0:
            messagebox.showinfo("成功", f"成功重命名 {success_count} 个文件/文件夹")
            self.clear_list()
            self._update_status(f"成功重命名 {success_count} 个文件/文件夹")
        else:
            messagebox.showwarning("部分成功", f"成功重命名 {success_count} 个文件/文件夹\n失败 {error_count} 个文件/文件夹")
            self._update_status(f"重命名完成，{error_count} 个失败", "orange")

    def handle_system_drop(self, files):
        """处理系统拖拽的文件 - 清空之前的数据，加载新数据"""
        # 清空之前的数据
        self.clear_list()

        added_any = False
        for file_path in files:
            if self.add_item(file_path):
                added_any = True
        if added_any:
            self.auto_reorder_items()
            self._update_status(f"已清空并重新加载 {len(files)} 个项目")

    def on_f5_refresh(self, event=None):
        """F5刷新列表中的文件/文件夹"""
        if not self.items:
            self._update_status("列表为空，无需刷新", "lightblue")
            return

        # 检查文件是否仍然存在，移除不存在的项目
        valid_items = []
        removed_count = 0

        for item in self.items:
            path_obj = Path(item['path'])
            if path_obj.exists():
                # 更新显示名称（可能文件名已改变）
                item['original_name'] = path_obj.name
                valid_items.append(item)
            else:
                removed_count += 1

        self.items = valid_items

        # 重新排序和显示
        if self.items:
            self.auto_reorder_items()

        if removed_count > 0:
            self._update_status(f"已刷新列表，移除了 {removed_count} 个不存在的项目", "orange")
        else:
            self._update_status("已刷新列表，所有项目有效", "lightblue")

    def _update_status(self, message, color="lightgreen"):
        """更新状态栏"""
        if self.status_callback:
            self.status_callback(message, color)
