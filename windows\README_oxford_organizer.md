# 牛津新世代数学教材整理工具

这是一个专门用于整理牛津新世代数学第三版教材文件夹的工具，可以自动按年级分类整理文件夹。

## 功能特点

- 🎯 **智能识别**: 自动识别4A、4B、5A、5B等年级文件夹
- 📁 **自动分类**: 按年级将文件夹移动到对应的目录
- 🖥️ **双模式**: 支持图形界面和命令行两种使用方式
- 👀 **预览功能**: 可以预览整理操作，确认后再执行
- 🔒 **安全操作**: 移动前会检查目标是否存在，避免意外覆盖

## 文件结构

```
windows/
├── oxford_math_organizer.py      # 核心整理类
├── organize_oxford_math.py       # 主要使用脚本
├── test_oxford_organizer.py      # 测试脚本
└── README_oxford_organizer.md    # 说明文档
```

## 使用方法

### 方法1: 快速整理（推荐）

直接运行主脚本：

```bash
python organize_oxford_math.py
```

然后选择 "1. 快速整理"，工具会使用预设的路径：
- 源路径: `D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）`
- 目标路径: `D:\BaiduNetdiskDownload\牛津新世代数学第三版（英文版）整理版本`

### 方法2: 图形界面

选择 "2. 图形界面"，会打开一个窗口，您可以：
1. 选择源文件夹和目标文件夹
2. 点击"扫描文件夹"查看需要整理的文件夹
3. 点击"创建目标结构"创建年级分类文件夹
4. 点击"预览整理"查看整理计划
5. 点击"执行整理"进行实际的文件夹移动

### 方法3: 仅预览

选择 "3. 仅预览"，可以查看将要进行的操作，不会实际移动任何文件。

## 整理规则

工具会按照以下规则整理文件夹：

### 源结构
```
牛津新世代数学第三版（英文版）/
├── A/
│   ├── 4A/
│   ├── 4B/
│   ├── 5A/
│   └── 5B/
├── B/
│   ├── 4A/
│   ├── 4B/
│   ├── 5A/
│   └── 5B/
└── C/
    ├── 4A/
    ├── 4B/
    ├── 5A/
    └── 5B/
```

### 目标结构
```
牛津新世代数学第三版（英文版）整理版本/
├── 1. 中四/
│   ├── A/
│   │   ├── 4A/
│   │   └── 4B/
│   ├── B/
│   │   ├── 4A/
│   │   └── 4B/
│   └── C/
│       ├── 4A/
│       └── 4B/
└── 2. 中五/
    ├── A/
    │   ├── 5A/
    │   └── 5B/
    ├── B/
    │   ├── 5A/
    │   └── 5B/
    └── C/
        ├── 5A/
        └── 5B/
```

## 程序化使用

如果您想在自己的代码中使用这个工具：

```python
from oxford_math_organizer import OxfordMathOrganizer

# 创建整理器实例
organizer = OxfordMathOrganizer()

# 执行整理
result = organizer.organize_folders_programmatically(
    source_dir="D:\\BaiduNetdiskDownload\\牛津新世代数学第三版（英文版）",
    target_dir="D:\\BaiduNetdiskDownload\\牛津新世代数学第三版（英文版）整理版本"
)

# 检查结果
if result["success"]:
    print(f"成功移动 {result['success_count']} 个文件夹")
else:
    print(f"操作失败: {result['error']}")
```

## 测试

运行测试脚本来验证工具功能：

```bash
python test_oxford_organizer.py
```

测试脚本会创建模拟的文件夹结构，然后测试整理功能。

## 注意事项

1. **备份重要数据**: 在使用工具之前，建议备份重要的文件夹
2. **路径检查**: 确保源路径和目标路径都存在且可访问
3. **权限问题**: 确保程序有足够的权限移动文件夹
4. **重复文件夹**: 如果目标位置已存在同名文件夹，工具会询问是否覆盖

## 自定义配置

您可以修改 `oxford_math_organizer.py` 中的配置来适应不同的需求：

```python
# 年级映射配置
self.grade_mapping = {
    '4': '1. 中四',
    '5': '2. 中五',
    '6': '3. 中六'  # 可以添加更多年级
}

# 年级前缀模式
self.grade_patterns = {
    '4': r'^4[A-Z]',  # 匹配4A, 4B等
    '5': r'^5[A-Z]',  # 匹配5A, 5B等
    '6': r'^6[A-Z]'   # 匹配6A, 6B等
}
```

## 故障排除

### 常见问题

1. **路径不存在**: 检查源路径和目标路径是否正确
2. **权限不足**: 以管理员身份运行程序
3. **文件夹被占用**: 确保没有其他程序正在使用要移动的文件夹
4. **中文路径问题**: 确保系统支持中文路径

### 错误日志

程序会显示详细的错误信息，帮助您诊断问题。如果遇到问题，请查看错误信息并相应处理。

## 许可证

此工具仅供个人学习和使用。
